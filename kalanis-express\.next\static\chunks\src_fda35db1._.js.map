{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"primary\" | \"secondary\" | \"accent\" | \"outline\" | \"ghost\";\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n  children: React.ReactNode;\n  className?: string;\n  loading?: boolean;\n  icon?: React.ReactNode;\n  iconPosition?: \"left\" | \"right\";\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      variant = \"primary\",\n      size = \"md\",\n      children,\n      className,\n      loading = false,\n      icon,\n      iconPosition = \"left\",\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = cn(\n      \"relative inline-flex items-center justify-center font-semibold transition-all duration-300 ease-out\",\n      \"focus:outline-none focus:ring-4 focus:ring-primary-500/20\",\n      \"disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\",\n      \"active:scale-95\"\n    );\n\n    const variantClasses = {\n      primary: cn(\n        \"bg-gradient-to-r from-primary-500 to-primary-600 text-white\",\n        \"hover:from-primary-600 hover:to-primary-700\",\n        \"shadow-luxury hover:shadow-luxury-lg hover:shadow-glow\",\n        \"hover:scale-105\"\n      ),\n      secondary: cn(\n        \"bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\",\n        \"hover:from-secondary-600 hover:to-secondary-700\",\n        \"shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-green\",\n        \"hover:scale-105\"\n      ),\n      accent: cn(\n        \"bg-gradient-to-r from-accent-500 to-accent-600 text-white\",\n        \"hover:from-accent-600 hover:to-accent-700\",\n        \"shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-gold\",\n        \"hover:scale-105\"\n      ),\n      outline: cn(\n        \"border-2 border-primary-500 text-primary-500 bg-transparent\",\n        \"hover:bg-primary-500 hover:text-white\",\n        \"hover:shadow-glow\"\n      ),\n      ghost: cn(\n        \"text-primary-500 bg-transparent\",\n        \"hover:bg-primary-50 hover:text-primary-600\"\n      ),\n    };\n\n    const sizeClasses = {\n      sm: \"px-4 py-2 text-sm rounded-xl\",\n      md: \"px-6 py-3 text-base rounded-2xl\",\n      lg: \"px-8 py-4 text-lg rounded-2xl\",\n      xl: \"px-10 py-5 text-xl rounded-3xl\",\n    };\n\n    const buttonClasses = cn(\n      baseClasses,\n      variantClasses[variant],\n      sizeClasses[size],\n      className\n    );\n\n    return (\n      <motion.button\n        ref={ref}\n        className={buttonClasses}\n        disabled={disabled || loading}\n        whileHover={{ scale: disabled ? 1 : 1.05 }}\n        whileTap={{ scale: disabled ? 1 : 0.95 }}\n        {...props}\n      >\n        {loading && (\n          <motion.div\n            className=\"mr-2\"\n            animate={{ rotate: 360 }}\n            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n          >\n            <svg\n              className=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              />\n            </svg>\n          </motion.div>\n        )}\n        \n        {icon && iconPosition === \"left\" && !loading && (\n          <span className=\"mr-2\">{icon}</span>\n        )}\n        \n        <span>{children}</span>\n        \n        {icon && iconPosition === \"right\" && !loading && (\n          <span className=\"ml-2\">{icon}</span>\n        )}\n      </motion.button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\nexport type { ButtonProps };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAgBA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CACE,EACE,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,SAAS,EACT,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,uGACA,6DACA,4EACA;IAGF,MAAM,iBAAiB;QACrB,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,+DACA,+CACA,0DACA;QAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mEACA,mDACA,gEACA;QAEF,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACP,6DACA,6CACA,+DACA;QAEF,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,+DACA,yCACA;QAEF,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACN,mCACA;IAEJ;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;IAGF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO,WAAW,IAAI;QAAK;QACzC,UAAU;YAAE,OAAO,WAAW,IAAI;QAAK;QACtC,GAAG,KAAK;;YAER,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;0BAE5D,cAAA,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,6LAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;YAMT,QAAQ,iBAAiB,UAAU,CAAC,yBACnC,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;0BAG1B,6LAAC;0BAAM;;;;;;YAEN,QAAQ,iBAAiB,WAAW,CAAC,yBACpC,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAIhC;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/text-generate-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { motion, stagger, useAnimate } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TextGenerateEffectProps {\n  words: string;\n  className?: string;\n  filter?: boolean;\n  duration?: number;\n}\n\nexport const TextGenerateEffect: React.FC<TextGenerateEffectProps> = ({\n  words,\n  className,\n  filter = true,\n  duration = 0.5,\n}) => {\n  const [scope, animate] = useAnimate();\n  const wordsArray = words.split(\" \");\n\n  useEffect(() => {\n    animate(\n      \"span\",\n      {\n        opacity: 1,\n        filter: filter ? \"blur(0px)\" : \"none\",\n      },\n      {\n        duration: duration ? duration : 1,\n        delay: stagger(0.2),\n      }\n    );\n  }, [scope.current, animate, duration, filter]);\n\n  const renderWords = () => {\n    return (\n      <motion.div ref={scope}>\n        {wordsArray.map((word, idx) => {\n          return (\n            <motion.span\n              key={word + idx}\n              className=\"dark:text-white text-black opacity-0\"\n              style={{\n                filter: filter ? \"blur(10px)\" : \"none\",\n              }}\n            >\n              {word}{\" \"}\n            </motion.span>\n          );\n        })}\n      </motion.div>\n    );\n  };\n\n  return (\n    <div className={cn(\"font-bold\", className)}>\n      <div className=\"mt-4\">\n        <div className=\"dark:text-white text-black text-2xl leading-snug tracking-wide\">\n          {renderWords()}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAaO,MAAM,qBAAwD,CAAC,EACpE,KAAK,EACL,SAAS,EACT,SAAS,IAAI,EACb,WAAW,GAAG,EACf;;IACC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD;IAClC,MAAM,aAAa,MAAM,KAAK,CAAC;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,QACE,QACA;gBACE,SAAS;gBACT,QAAQ,SAAS,cAAc;YACjC,GACA;gBACE,UAAU,WAAW,WAAW;gBAChC,OAAO,CAAA,GAAA,mLAAA,CAAA,UAAO,AAAD,EAAE;YACjB;QAEJ;uCAAG;QAAC,MAAM,OAAO;QAAE;QAAS;QAAU;KAAO;IAE7C,MAAM,cAAc;QAClB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAAC,KAAK;sBACd,WAAW,GAAG,CAAC,CAAC,MAAM;gBACrB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,WAAU;oBACV,OAAO;wBACL,QAAQ,SAAS,eAAe;oBAClC;;wBAEC;wBAAM;;mBANF,OAAO;;;;;YASlB;;;;;;IAGN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;;;;;AAKX;GApDa;;QAMc,0LAAA,CAAA,aAAU;;;KANxB", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/background-gradient.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React from \"react\";\nimport { motion } from \"motion/react\";\n\ninterface BackgroundGradientProps {\n  children?: React.ReactNode;\n  className?: string;\n  containerClassName?: string;\n  animate?: boolean;\n}\n\nexport const BackgroundGradient: React.FC<BackgroundGradientProps> = ({\n  children,\n  className,\n  containerClassName,\n  animate = true,\n}) => {\n  const variants = {\n    initial: {\n      backgroundPosition: \"0 50%\",\n    },\n    animate: {\n      backgroundPosition: [\"0, 50%\", \"100% 50%\", \"0 50%\"],\n    },\n  };\n\n  return (\n    <div className={cn(\"relative p-[4px] group\", containerClassName)}>\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] opacity-60 group-hover:opacity-100 blur-xl transition duration-500 will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#00ccb1,transparent),radial-gradient(circle_farthest-side_at_100%_0,#7b61ff,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#ffc414,transparent),radial-gradient(circle_farthest-side_at_0_0,#1ca0fb,#141316)]\"\n        )}\n      />\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#00ccb1,transparent),radial-gradient(circle_farthest-side_at_100%_0,#7b61ff,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#ffc414,transparent),radial-gradient(circle_farthest-side_at_0_0,#1ca0fb,#141316)]\"\n        )}\n      />\n\n      <div className={cn(\"relative z-10\", className)}>{children}</div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAaO,MAAM,qBAAwD,CAAC,EACpE,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,UAAU,IAAI,EACf;IACC,MAAM,WAAW;QACf,SAAS;YACP,oBAAoB;QACtB;QACA,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAQ;QACrD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;0BAC3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+HACA;;;;;;0BAGJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;;;;;;0BAIJ,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;0BAAa;;;;;;;;;;;;AAGvD;KA/Da", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { TextGenerateEffect } from \"@/components/ui/text-generate-effect\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport { \n  IconShieldCheck, \n  IconClock, \n  IconMapPin, \n  IconCertificate,\n  IconPhone,\n  IconCalendar\n} from \"@tabler/icons-react\";\n\nconst Hero: React.FC = () => {\n  const heroText = \"Professional Mobile Drug & DNA Testing Services - We Come to You\";\n  const subText = \"DOT Compliant • Certified Technicians • Accurate Results • Convenient Scheduling\";\n\n  const features = [\n    {\n      icon: <IconMapPin className=\"w-6 h-6\" />,\n      title: \"Mobile Service\",\n      description: \"We come to your location\"\n    },\n    {\n      icon: <IconShieldCheck className=\"w-6 h-6\" />,\n      title: \"DOT Certified\",\n      description: \"Fully compliant testing\"\n    },\n    {\n      icon: <IconClock className=\"w-6 h-6\" />,\n      title: \"Fast Results\",\n      description: \"Quick turnaround times\"\n    },\n    {\n      icon: <IconCertificate className=\"w-6 h-6\" />,\n      title: \"Accurate Testing\",\n      description: \"Medical-grade precision\"\n    }\n  ];\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]\" />\n      \n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-72 h-72 bg-primary-500/10 rounded-full blur-3xl\"\n        animate={{\n          x: [0, 100, 0],\n          y: [0, -50, 0],\n        }}\n        transition={{\n          duration: 20,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-20 right-10 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl\"\n        animate={{\n          x: [0, -80, 0],\n          y: [0, 60, 0],\n        }}\n        transition={{\n          duration: 25,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n\n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Content */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n          >\n            {/* Badge */}\n            <motion.div\n              className=\"inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n            >\n              <IconShieldCheck className=\"w-4 h-4 mr-2\" />\n              Trusted by 500+ Businesses\n            </motion.div>\n\n            {/* Main Heading */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-5xl lg:text-7xl font-bold leading-tight\">\n                <span className=\"text-gradient-luxury\">Kalanis</span>\n                <br />\n                <span className=\"text-neutral-900\">Express</span>\n              </h1>\n              \n              <div className=\"text-xl lg:text-2xl text-neutral-600 max-w-2xl\">\n                <TextGenerateEffect \n                  words={heroText}\n                  className=\"text-xl lg:text-2xl text-neutral-600\"\n                />\n              </div>\n            </div>\n\n            {/* Subtitle */}\n            <motion.p\n              className=\"text-lg text-neutral-500 max-w-xl leading-relaxed\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.6 }}\n            >\n              {subText}\n            </motion.p>\n\n            {/* CTA Buttons */}\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.6 }}\n            >\n              <Button\n                size=\"lg\"\n                variant=\"primary\"\n                icon={<IconCalendar className=\"w-5 h-5\" />}\n                className=\"text-lg px-8 py-4\"\n              >\n                Schedule Testing\n              </Button>\n              \n              <Button\n                size=\"lg\"\n                variant=\"outline\"\n                icon={<IconPhone className=\"w-5 h-5\" />}\n                className=\"text-lg px-8 py-4\"\n              >\n                Call Now: (*************\n              </Button>\n            </motion.div>\n\n            {/* Trust Indicators */}\n            <motion.div\n              className=\"flex items-center space-x-6 pt-8\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.2, duration: 0.6 }}\n            >\n              <div className=\"text-sm text-neutral-500\">\n                <div className=\"font-semibold text-neutral-900\">24/7</div>\n                <div>Available</div>\n              </div>\n              <div className=\"w-px h-8 bg-neutral-300\" />\n              <div className=\"text-sm text-neutral-500\">\n                <div className=\"font-semibold text-neutral-900\">Same Day</div>\n                <div>Service</div>\n              </div>\n              <div className=\"w-px h-8 bg-neutral-300\" />\n              <div className=\"text-sm text-neutral-500\">\n                <div className=\"font-semibold text-neutral-900\">100%</div>\n                <div>Compliant</div>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Right Column - Features Grid */}\n          <motion.div\n            className=\"grid grid-cols-2 gap-6\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4, ease: \"easeOut\" }}\n          >\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}\n              >\n                <BackgroundGradient className=\"p-6 rounded-3xl\">\n                  <div className=\"bg-white/80 backdrop-blur-xl rounded-2xl p-6 h-full\">\n                    <div className=\"flex flex-col items-center text-center space-y-4\">\n                      <div className=\"p-3 rounded-2xl bg-primary-100 text-primary-600\">\n                        {feature.icon}\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-neutral-900 mb-2\">\n                          {feature.title}\n                        </h3>\n                        <p className=\"text-sm text-neutral-600\">\n                          {feature.description}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </BackgroundGradient>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 1.5, duration: 0.6 }}\n      >\n        <motion.div\n          className=\"w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <motion.div\n            className=\"w-1 h-3 bg-neutral-400 rounded-full mt-2\"\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAgBA,MAAM,OAAiB;IACrB,MAAM,WAAW;IACjB,MAAM,UAAU;IAEhB,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,6NAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,2NAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAK;qBAAE;oBACd,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAChB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBACf;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;;8CAG7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;;sDAExC,6LAAC,uOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAK9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,6LAAC;;;;;8DACD,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAGrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yJAAA,CAAA,qBAAkB;gDACjB,OAAO;gDACP,WAAU;;;;;;;;;;;;;;;;;8CAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;8CAEvC;;;;;;8CAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAG,UAAU;oCAAI;;sDAEtC,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,oBAAM,6LAAC,iOAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAC9B,WAAU;sDACX;;;;;;sDAID,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,oBAAM,6LAAC,2NAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAC3B,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,6LAAC;8DAAI;;;;;;;;;;;;sDAEP,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,6LAAC;8DAAI;;;;;;;;;;;;sDAEP,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAMX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;sCAExD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;wCAAK,UAAU;oCAAI;8CAEtD,cAAA,6LAAC,qJAAA,CAAA,qBAAkB;wCAAC,WAAU;kDAC5B,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI;;;;;;kEAEf,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhBzB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BA6B5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;0BAExC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAE5C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;;;;;;;;;;;;;;;;;;;;;AAMxD;KArNM;uCAuNS", "debugId": null}}]}