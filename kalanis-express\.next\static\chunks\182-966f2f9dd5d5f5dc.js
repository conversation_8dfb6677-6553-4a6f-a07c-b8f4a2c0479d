"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[182],{10:(t,e,i)=>{i.d(e,{V:()=>u,f:()=>m});var n=i(4272);let s=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(614),a=i(1557);let o="number",l="color",h=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function u(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},r=[],a=0,u=e.replace(h,t=>(n.y.test(t)?(s.color.push(a),r.push(l),i.push(n.y.parse(t))):t.startsWith("var(")?(s.var.push(a),r.push("var"),i.push(t)):(s.number.push(a),r.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:u,indexes:s,types:r}}function d(t){return u(t).values}function c(t){let{split:e,types:i}=u(t),s=e.length;return t=>{let r="";for(let h=0;h<s;h++)if(r+=e[h],void 0!==t[h]){let e=i[h];e===o?r+=(0,a.a)(t[h]):e===l?r+=n.y.transform(t[h]):r+=t[h]}return r}}let p=t=>"number"==typeof t?0:n.y.test(t)?n.y.getAnimatableNone(t):t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(r.S)?.length||0)+(t.match(s)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},18:(t,e,i)=>{i.d(e,{U:()=>n,f:()=>s});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],s=new Set(n)},98:(t,e,i)=>{i.d(e,{OQ:()=>u,bt:()=>l});var n=i(5626),s=i(2923),r=i(4261),a=i(9515);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class h{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=r.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function u(t,e){return new h(t,e)}},144:(t,e,i)=>{i.d(e,{E:()=>o});var n=i(6330),s=i(8498),r=i(4687);let a={decay:n.B,inertia:n.B,tween:s.i,keyframes:s.i,spring:r.o};function o(t){"string"==typeof t.type&&(t.type=a[t.type])}},182:(t,e,i)=>{i.d(e,{P:()=>iM});var n,s,r=i(6340),a=i(419),o=i(7934);function l(t,e,i={}){let n=(0,a.K)(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all((0,o.$)(t,n,i)):()=>Promise.resolve(),u=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=1,r){let a=[],o=(t.variantChildren.size-1)*n,u=1===s?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(h).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(l(t,e,{...r,delay:i+u(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+n,a,o,i)}:()=>Promise.resolve(),{when:d}=s;if(!d)return Promise.all([r(),u(i.delay)]);{let[t,e]="beforeChildren"===d?[r,u]:[u,r];return t().then(()=>e())}}function h(t,e){return t.sortNodePosition(e)}var u=i(5910);function d(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}var c=i(5305),p=i(8312);let m=p._.length,f=[...p.U].reverse(),v=p.U.length;function g(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function y(){return{animate:g(!0),whileInView:g(),whileHover:g(),whileTap:g(),whileDrag:g(),whileFocus:g(),exit:g()}}class x{constructor(t){this.isMounted=!1,this.node=t}update(){}}class T extends x{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>l(t,e,i)));else if("string"==typeof e)n=l(t,e,i);else{let s="function"==typeof e?(0,a.K)(t,e,i.custom):e;n=Promise.all((0,o.$)(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=y(),n=!0,s=e=>(i,n)=>{let s=(0,a.K)(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function h(o){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<m;t++){let n=p._[t],s=e.props[n];((0,c.w)(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},g=[],y=new Set,x={},T=1/0;for(let e=0;e<v;e++){var w,b;let a=f[e],p=i[a],m=void 0!==l[a]?l[a]:h[a],v=(0,c.w)(m),P=a===o?p.isActive:null;!1===P&&(T=e);let S=m===h[a]&&m!==l[a]&&v;if(S&&n&&t.manuallyAnimateOnMount&&(S=!1),p.protectedKeys={...x},!p.isActive&&null===P||!m&&!p.prevProp||(0,r.N)(m)||"boolean"==typeof m)continue;let A=(w=p.prevProp,"string"==typeof(b=m)?b!==w:!!Array.isArray(b)&&!d(b,w)),V=A||a===o&&p.isActive&&!S&&v||e>T&&v,M=!1,k=Array.isArray(m)?m:[m],E=k.reduce(s(a),{});!1===P&&(E={});let{prevResolvedValues:D={}}=p,C={...D,...E},R=e=>{V=!0,y.has(e)&&(M=!0,y.delete(e)),p.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=E[t],i=D[t];if(x.hasOwnProperty(t))continue;let n=!1;((0,u.p)(e)&&(0,u.p)(i)?d(e,i):e===i)?void 0!==e&&y.has(t)?R(t):p.protectedKeys[t]=!0:null!=e?R(t):y.add(t)}p.prevProp=m,p.prevResolvedValues=E,p.isActive&&(x={...x,...E}),n&&t.blockInitialAnimation&&(V=!1);let j=!(S&&A)||M;V&&j&&g.push(...k.map(t=>({animation:t,options:{type:a}})))}if(y.size){let e={};if("boolean"!=typeof l.initial){let i=(0,a.K)(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}y.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),g.push({animation:e})}let P=!!g.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(P=!1),n=!1,P?e(g):Promise.resolve()}return{animateChanges:h,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=h(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=y(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,r.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let w=0;class b extends x{constructor(){super(...arguments),this.id=w++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}var P=i(9827);let S={x:!1,y:!1};var A=i(4158),V=i(9515),M=i(3210),k=i(4542),E=i(6335);function D(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let C=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function R(t){return{point:{x:t.pageX,y:t.pageY}}}let j=t=>e=>C(e)&&t(e,R(e));function L(t,e,i,n){return D(t,e,j(i),n)}var B=i(8588);function F(t){return t.max-t.min}function O(t,e,i,n=.5){t.origin=n,t.originPoint=(0,M.k)(e.min,e.max,t.origin),t.scale=F(i)/F(e),t.translate=(0,M.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function I(t,e,i,n){O(t.x,e.x,i.x,n?n.originX:void 0),O(t.y,e.y,i.y,n?n.originY:void 0)}function U(t,e,i){t.min=i.min+e.min,t.max=t.min+F(e)}function W(t,e,i){t.min=e.min-i.min,t.max=t.min+F(e)}function $(t,e,i){W(t.x,e.x,i.x),W(t.y,e.y,i.y)}var N=i(1786);function q(t){return[t("x"),t("y")]}var G=i(3757);let X=({current:t})=>t?t.ownerDocument.defaultView:null;function K(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var H=i(6333),Y=i(3191),z=i(7215);let Q=(t,e)=>Math.abs(t-e);class Z{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=tt(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(Q(t.x,e.x)**2+Q(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=V.uv;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=_(e,this.transformPagePoint),V.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=tt("pointercancel"===t.type?this.lastMoveEventInfo:_(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!C(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let r=_(R(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=V.uv;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,tt(r,this.history)),this.removeListeners=(0,Y.F)(L(this.contextWindow,"pointermove",this.handlePointerMove),L(this.contextWindow,"pointerup",this.handlePointerUp),L(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,V.WG)(this.updatePoint)}}function _(t,e){return e?{point:e(t.point)}:t}function J(t,e){return{x:t.x-e.x,y:t.y-e.y}}function tt({point:t},e){return{point:t,delta:J(t,te(e)),offset:J(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=te(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>(0,z.f)(.1)));)i--;if(!n)return{x:0,y:0};let r=(0,z.X)(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function te(t){return t[t.length-1]}var ti=i(5818),tn=i(1297);function ts(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function tr(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function ta(t,e,i){return{min:to(t,e),max:to(t,i)}}function to(t,e){return"number"==typeof t?t:t[e]||0}let tl=new WeakMap;class th{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,N.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new Z(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(R(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(S[t])return null;else return S[t]=!0,()=>{S[t]=!1};return S.x||S.y?null:(S.x=S.y=!0,()=>{S.x=S.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),q(t=>{let e=this.getAxisMotionValue(t).get()||0;if(A.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=F(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&V.Gt.postRender(()=>s(t,e)),(0,H.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>q(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:X(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:s}=this.getProps();s&&V.Gt.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!tu(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?(0,M.k)(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?(0,M.k)(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&K(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:ts(t.x,i,s),y:ts(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:ta(t,"left","right"),y:ta(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&q(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!K(e))return!1;let n=e.current;(0,k.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=(0,G.L)(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:tr(t.x,r.x),y:tr(t.y,r.y)});if(i){let t=i((0,B.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,B.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(q(a=>{if(!tu(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,H.g)(this.visualElement,t),i.start((0,E.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){q(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){q(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){q(e=>{let{drag:i}=this.getProps();if(!tu(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-(0,M.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!K(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};q(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=F(t),s=F(e);return s>n?i=(0,ti.q)(e.min,e.max-n,t.min):n>s&&(i=(0,ti.q)(t.min,t.max-s,e.min)),(0,tn.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),q(e=>{if(!tu(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set((0,M.k)(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;tl.set(this.visualElement,this);let t=L(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();K(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.Gt.read(e);let s=D(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(q(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function tu(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class td extends x{constructor(t){super(t),this.removeGroupControls=P.l,this.removeListeners=P.l,this.controls=new th(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||P.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let tc=t=>(e,i)=>{t&&V.Gt.postRender(()=>t(e,i))};class tp extends x{constructor(){super(...arguments),this.removePointerDownListener=P.l}onPointerDown(t){this.session=new Z(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:X(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:tc(t),onStart:tc(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&V.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=L(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var tm=i(5155);let{schedule:tf}=(0,i(8437).I)(queueMicrotask,!1);var tv=i(2115),tg=i(2082),ty=i(869);let tx=(0,tv.createContext)({}),tT={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tw(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let tb={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!A.px.test(t))return t;else t=parseFloat(t);let i=tw(t,e.target.x),n=tw(t,e.target.y);return`${i}% ${n}%`}};var tP=i(10),tS=i(637);class tA extends tv.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;(0,tS.$)(tM),s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),tT.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||V.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tf.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function tV(t){let[e,i]=(0,tg.xQ)(),n=(0,tv.useContext)(ty.L);return(0,tm.jsx)(tA,{...t,layoutGroup:n,switchLayoutGroup:(0,tv.useContext)(tx),isPresent:e,safeToRemove:i})}let tM={borderRadius:{...tb,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tb,borderTopRightRadius:tb,borderBottomLeftRadius:tb,borderBottomRightRadius:tb,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tP.f.parse(t);if(n.length>5)return t;let s=tP.f.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=(0,M.k)(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}};var tk=i(4744),tE=i(9782),tD=i(5943),tC=i(8777),tR=i(4261),tj=i(3704),tL=i(98),tB=i(5626),tF=i(5580),tO=i(6926),tI=i(6668);let tU=(t,e)=>t.depth-e.depth;class tW{constructor(){this.children=[],this.isDirty=!1}add(t){(0,tI.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,tI.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(tU),this.isDirty=!1,this.children.forEach(t)}}var t$=i(4803);function tN(t){return(0,t$.S)(t)?t.get():t}var tq=i(7712);let tG=["TopLeft","TopRight","BottomLeft","BottomRight"],tX=tG.length,tK=t=>"string"==typeof t?parseFloat(t):t,tH=t=>"number"==typeof t||A.px.test(t);function tY(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let tz=tZ(0,.5,tq.yT),tQ=tZ(.5,.95,P.l);function tZ(t,e,i){return n=>n<t?0:n>e?1:i((0,ti.q)(t,e,n))}function t_(t,e){t.min=e.min,t.max=e.max}function tJ(t,e){t_(t.x,e.x),t_(t.y,e.y)}function t0(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var t1=i(6147);function t5(t,e,i,n,s){return t-=e,t=(0,t1.hq)(t,1/i,n),void 0!==s&&(t=(0,t1.hq)(t,1/s,n)),t}function t2(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(A.KN.test(e)&&(e=parseFloat(e),e=(0,M.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=(0,M.k)(r.min,r.max,n);t===r&&(o-=e),t.min=t5(t.min,e,i,o,s),t.max=t5(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let t3=["x","scaleX","originX"],t8=["y","scaleY","originY"];function t4(t,e,i,n){t2(t.x,e,t3,i?i.x:void 0,n?n.x:void 0),t2(t.y,e,t8,i?i.y:void 0,n?n.y:void 0)}function t7(t){return 0===t.translate&&1===t.scale}function t6(t){return t7(t.x)&&t7(t.y)}function t9(t,e){return t.min===e.min&&t.max===e.max}function et(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ee(t,e){return et(t.x,e.x)&&et(t.y,e.y)}function ei(t){return F(t.x)/F(t.y)}function en(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class es{constructor(){this.members=[]}add(t){(0,tI.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,tI.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var er=i(2662);let ea={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},eo=["","X","Y","Z"],el={visibility:"hidden"},eh=0;function eu(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function ed({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=eh++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tk.Q.value&&(ea.nodes=ea.calculatedTargetDeltas=ea.calculatedProjections=0),this.nodes.forEach(em),this.nodes.forEach(ew),this.nodes.forEach(eb),this.nodes.forEach(ef),tk.Q.addProjectionMetrics&&tk.Q.addProjectionMetrics(ea)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new tW)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tB.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,tE.x)(e)&&!(0,tD.h)(e),this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tR.k.now(),n=({timestamp:s})=>{let r=s-i;r>=250&&((0,V.WG)(n),t(r-e))};return V.Gt.setup(n,!0),()=>(0,V.WG)(n)}(n,250),tT.hasAnimatedSinceResize&&(tT.hasAnimatedSinceResize=!1,this.nodes.forEach(eT))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||ek,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!ee(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,tC.r)(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||eT(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,V.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eP),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=(0,tO.P)(i);if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",V.Gt,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eg);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(ey);this.isUpdating||this.nodes.forEach(ey),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(ex),this.nodes.forEach(ec),this.nodes.forEach(ep),this.clearAllSnapshots();let t=tR.k.now();V.uv.delta=(0,tn.q)(0,1e3/60,t-V.uv.timestamp),V.uv.timestamp=t,V.uv.isProcessing=!0,V.PP.update.process(V.uv),V.PP.preRender.process(V.uv),V.PP.render.process(V.uv),V.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tf.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ev),this.sharedNodes.forEach(eS)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||F(this.snapshot.measuredBox.x)||F(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,N.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!t6(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,er.HD)(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),eC((e=n).x),eC(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,N.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ej))){let{scroll:t}=this.root;t&&((0,t1.Ql)(e.x,t.offset.x),(0,t1.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,N.ge)();if(tJ(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&tJ(e,t),(0,t1.Ql)(e.x,s.offset.x),(0,t1.Ql)(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=(0,N.ge)();tJ(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&(0,t1.Ww)(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),(0,er.HD)(n.latestValues)&&(0,t1.Ww)(i,n.latestValues)}return(0,er.HD)(this.latestValues)&&(0,t1.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,N.ge)();tJ(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,er.HD)(i.latestValues))continue;(0,er.vk)(i.latestValues)&&i.updateSnapshot();let n=(0,N.ge)();tJ(n,i.measurePageBox()),t4(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return(0,er.HD)(this.latestValues)&&t4(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==V.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=V.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,N.ge)(),this.relativeTargetOrigin=(0,N.ge)(),$(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),tJ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,N.ge)(),this.targetWithTransforms=(0,N.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,U(r.x,a.x,o.x),U(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):tJ(this.target,this.layout.layoutBox),(0,t1.o4)(this.target,this.targetDelta)):tJ(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,N.ge)(),this.relativeTargetOrigin=(0,N.ge)(),$(this.relativeTargetOrigin,this.target,t.target),tJ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tk.Q.value&&ea.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,er.vk)(this.parent.latestValues)||(0,er.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===V.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;tJ(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;(0,t1.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,N.ge)());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(t0(this.prevProjectionDelta.x,this.projectionDelta.x),t0(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),I(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&en(this.projectionDelta.x,this.prevProjectionDelta.x)&&en(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),tk.Q.value&&ea.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,N.xU)(),this.projectionDelta=(0,N.xU)(),this.projectionDeltaWithTransform=(0,N.xU)()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=(0,N.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=(0,N.ge)(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(eM));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(eA(a.x,t.x,n),eA(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,v;$(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,v=n,eV(p.x,m.x,f.x,v),eV(p.y,m.y,f.y,v),i&&(h=this.relativeTarget,c=i,t9(h.x,c.x)&&t9(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=(0,N.ge)()),tJ(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=(0,M.k)(0,i.opacity??1,tz(n)),t.opacityExit=(0,M.k)(e.opacity??1,0,tQ(n))):r&&(t.opacity=(0,M.k)(e.opacity??1,i.opacity??1,n));for(let s=0;s<tX;s++){let r=`border${tG[s]}Radius`,a=tY(e,r),o=tY(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||tH(a)===tH(o)?(t[r]=Math.max((0,M.k)(tK(a),tK(o),n),0),(A.KN.test(o)||A.KN.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=(0,M.k)(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,V.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.Gt.update(()=>{tT.hasAnimatedSinceResize=!0,tj.q.layout++,this.motionValue||(this.motionValue=(0,tL.OQ)(0)),this.currentAnimation=(0,tF.z)(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{tj.q.layout--},onComplete:()=>{tj.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&eR(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||(0,N.ge)();let e=F(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=F(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}tJ(e,i),(0,t1.Ww)(e,s),I(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new es),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&eu("z",t,n,this.animationValues);for(let e=0;e<eo.length;e++)eu(`rotate${eo[e]}`,t,n,this.animationValues),eu(`skew${eo[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return el;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=tN(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tN(t?.pointerEvents)||""),this.hasProjected&&!(0,er.HD)(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,tS.H){if(void 0===s[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=tS.H[t],o="none"===e.transform?s[t]:i(s[t],n);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=n===this?tN(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(eg),this.root.sharedNodes.clear()}}}function ec(t){t.updateLayout()}function ep(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?q(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=F(n);n.min=i[t].min,n.max=n.min+s}):eR(s,e.layoutBox,i)&&q(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=F(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=(0,N.xU)();I(a,i,e.layoutBox);let o=(0,N.xU)();r?I(o,t.applyTransform(n,!0),e.measuredBox):I(o,i,e.layoutBox);let l=!t6(a),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=(0,N.ge)();$(a,e.layoutBox,s.layoutBox);let o=(0,N.ge)();$(o,i,r.layoutBox),ee(a,o)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function em(t){tk.Q.value&&ea.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ef(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ev(t){t.clearSnapshot()}function eg(t){t.clearMeasurements()}function ey(t){t.isLayoutDirty=!1}function ex(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function eT(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ew(t){t.resolveTargetDelta()}function eb(t){t.calcProjection()}function eP(t){t.resetSkewAndRotation()}function eS(t){t.removeLeadSnapshot()}function eA(t,e,i){t.translate=(0,M.k)(e.translate,0,i),t.scale=(0,M.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function eV(t,e,i,n){t.min=(0,M.k)(e.min,i.min,n),t.max=(0,M.k)(e.max,i.max,n)}function eM(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ek={duration:.45,ease:[.4,0,.1,1]},eE=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),eD=eE("applewebkit/")&&!eE("chrome/")?Math.round:P.l;function eC(t){t.min=eD(t.min),t.max=eD(t.max)}function eR(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ei(e)-ei(i)))}function ej(t){return t!==t.root&&t.scroll?.wasRoot}let eL=ed({attachResizeListener:(t,e)=>D(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eB={current:void 0},eF=ed({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!eB.current){let t=new eL({});t.mount(window),t.setOptions({layoutScroll:!0}),eB.current=t}return eB.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var eO=i(2198);function eI(t,e){let i=(0,eO.K)(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function eU(t){return!("touch"===t.pointerType||S.x||S.y)}function eW(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&V.Gt.postRender(()=>s(e,R(e)))}class e$ extends x{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=eI(t,i),a=t=>{if(!eU(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{eU(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(eW(this.node,e,"Start"),t=>eW(this.node,t,"End"))))}unmount(){}}class eN extends x{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,Y.F)(D(this.node.current,"focus",()=>this.onFocus()),D(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var eq=i(7351);let eG=(t,e)=>!!e&&(t===e||eG(t,e.parentElement)),eX=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),eK=new WeakSet;function eH(t){return e=>{"Enter"===e.key&&t(e)}}function eY(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ez=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=eH(()=>{if(eK.has(i))return;eY(i,"down");let t=eH(()=>{eY(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>eY(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function eQ(t){return C(t)&&!(S.x||S.y)}function eZ(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&V.Gt.postRender(()=>s(e,R(e)))}class e_ extends x{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=eI(t,i),a=t=>{let n=t.currentTarget;if(!eQ(t))return;eK.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),eK.has(n)&&eK.delete(n),eQ(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||eG(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),(0,eq.s)(t))&&(t.addEventListener("focus",t=>ez(t,s)),eX.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(eZ(this.node,e,"Start"),(t,{success:e})=>eZ(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let eJ=new WeakMap,e0=new WeakMap,e1=t=>{let e=eJ.get(t.target);e&&e(t)},e5=t=>{t.forEach(e1)},e2={some:0,all:1};class e3 extends x{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:e2[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;e0.has(i)||e0.set(i,{});let n=e0.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(e5,{root:t,...e})),n[s]}(e);return eJ.set(t,i),n.observe(t),()=>{eJ.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let e8=(0,tv.createContext)({strict:!1});var e4=i(1508);let e7=(0,tv.createContext)({});var e6=i(9253);function e9(t){return Array.isArray(t)?t.join(" "):t}var it=i(8972),ie=i(6642);let ii=Symbol.for("motionComponentSymbol");var is=i(1788),ir=i(845),ia=i(7494),io=i(3055),il=i(7684);let ih=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iu(t,e,i){for(let n in e)(0,t$.S)(e[n])||(0,io.z)(n,i)||(t[n]=e[n])}var id=i(2076);let ic=()=>({...ih(),attrs:{}});var ip=i(3095);let im=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function iv(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||im.has(t)}let ig=t=>!iv(t);try{!function(t){"function"==typeof t&&(ig=e=>e.startsWith("on")?!iv(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let iy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ix(t){if("string"!=typeof t||t.includes("-"));else if(iy.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var iT=i(2735),iw=i(2885);let ib=t=>(e,i)=>{let n=(0,tv.useContext)(e7),s=(0,tv.useContext)(ir.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=tN(a[t]);let{initial:o,animate:l}=t,h=(0,e6.e)(t),u=(0,e6.O)(t);e&&u&&!h&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===l&&(l=e.animate));let d=!!i&&!1===i.initial,c=(d=d||!1===o)?l:o;if(c&&"boolean"!=typeof c&&!(0,r.N)(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let n=(0,iT.a)(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,iw.M)(a)},iP={useVisualState:ib({scrapeMotionValuesFromProps:i(8609).x,createRenderState:ih})},iS={useVisualState:ib({scrapeMotionValuesFromProps:i(4527).x,createRenderState:ic})};var iA=i(5245),iV=i(728);let iM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((n={animation:{Feature:T},exit:{Feature:b},inView:{Feature:e3},tap:{Feature:e_},focus:{Feature:eN},hover:{Feature:e$},pan:{Feature:tp},drag:{Feature:td,ProjectionNode:eF,MeasureLayout:tV},layout:{ProjectionNode:eF,MeasureLayout:tV}},s=(t,e)=>ix(t)?new iV.l(e):new iA.M(e,{allowProjection:t!==tv.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:s,useRender:r,useVisualState:a,Component:o}=t;function l(t,e){var i,n,l;let h,u={...(0,tv.useContext)(e4.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,tv.useContext)(ty.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,p=function(t){let{initial:e,animate:i}=function(t,e){if((0,e6.e)(t)){let{initial:e,animate:i}=t;return{initial:!1===e||(0,c.w)(e)?e:void 0,animate:(0,c.w)(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,tv.useContext)(e7));return(0,tv.useMemo)(()=>({initial:e,animate:i}),[e9(e),e9(i)])}(t),m=a(t,d);if(!d&&it.B){n=0,l=0,(0,tv.useContext)(e8).strict;let t=function(t){let{drag:e,layout:i}=ie.B;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);h=t.MeasureLayout,p.visualElement=function(t,e,i,n,s){let{visualElement:r}=(0,tv.useContext)(e7),a=(0,tv.useContext)(e8),o=(0,tv.useContext)(ir.t),l=(0,tv.useContext)(e4.Q).reducedMotion,h=(0,tv.useRef)(null);n=n||a.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let u=h.current,d=(0,tv.useContext)(tx);u&&!u.projection&&s&&("html"===u.type||"svg"===u.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&K(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,s,d);let c=(0,tv.useRef)(!1);(0,tv.useInsertionEffect)(()=>{u&&c.current&&u.update(i,o)});let p=i[is.n],m=(0,tv.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,ia.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),tf.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,tv.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(o,m,u,s,t.ProjectionNode)}return(0,tm.jsxs)(e7.Provider,{value:p,children:[h&&p.visualElement?(0,tm.jsx)(h,{visualElement:p.visualElement,...u}):null,r(o,t,(i=p.visualElement,(0,tv.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):K(e)&&(e.current=t))},[i])),m,d,p.visualElement)]})}n&&function(t){for(let e in t)ie.B[e]={...ie.B[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let h=(0,tv.forwardRef)(l);return h[ii]=o,h}({...ix(t)?iS:iP,preloadedFeatures:n,useRender:function(t=!1){return(e,i,n,{latestValues:s},r)=>{let a=(ix(e)?function(t,e,i,n){let s=(0,tv.useMemo)(()=>{let i=ic();return(0,id.B)(i,e,(0,ip.n)(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};iu(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return iu(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,tv.useMemo)(()=>{let i=ih();return(0,il.O)(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,s,r,e),o=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(ig(s)||!0===i&&iv(s)||!e&&!iv(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(i,"string"==typeof e,t),l=e!==tv.Fragment?{...o,...a,ref:n}:{},{children:h}=i,u=(0,tv.useMemo)(()=>(0,t$.S)(h)?h.get():h,[h]);return(0,tv.createElement)(e,{...l,children:u})}}(e),createVisualElement:s,Component:t})}))},280:(t,e,i)=>{i.d(e,{E4:()=>o,Hr:()=>d,W9:()=>u});var n=i(4160),s=i(18),r=i(7887),a=i(4158);let o=t=>t===r.ai||t===a.px,l=new Set(["x","y","z"]),h=s.U.filter(t=>!l.has(t));function u(t){let e=[];return h.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}let d={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>(0,n.ry)(e,"x"),y:(t,{transform:e})=>(0,n.ry)(e,"y")};d.translateX=d.x,d.translateY=d.y},419:(t,e,i)=>{i.d(e,{K:()=>s});var n=i(2735);function s(t,e,i){let s=t.getProps();return(0,n.a)(s,e,void 0!==i?i:s.custom,t)}},532:(t,e,i)=>{i.d(e,{s:()=>y});var n=i(3191),s=i(1297),r=i(7215),a=i(4261),o=i(3704),l=i(6087),h=i(9515);let u=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>h.Gt.update(e,t),stop:()=>(0,h.WG)(e),now:()=>h.uv.isProcessing?h.uv.timestamp:a.k.now()}};var d=i(6330),c=i(8498),p=i(2458),m=i(6778),f=i(144),v=i(1513);let g=t=>t/100;class y extends v.q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==a.k.now()&&this.tick(a.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},o.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,f.E)(t);let{type:e=c.i,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:a=0}=t,{keyframes:o}=t,h=e||c.i;h!==c.i&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,n.F)(g,(0,l.j)(o[0],o[1])),o=[0,100]);let u=h({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=h({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=(0,p.t)(u));let{calculatedDuration:d}=u;this.calculatedDuration=d,this.resolvedDuration=d+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:h=0,keyframes:u,repeat:c,repeatType:p,repeatDelay:f,type:v,onUpdate:g,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-h*(this.playbackSpeed>=0?1:-1),T=this.playbackSpeed>=0?x<0:x>n;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let w=this.currentTime,b=i;if(c){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,f&&(i-=f/o)):"mirror"===p&&(b=a)),w=(0,s.q)(0,1,i)*o}let P=T?{done:!1,value:u[0]}:b.next(w);r&&(P.value=r(P.value));let{done:S}=P;T||null===l||(S=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return A&&v!==d.B&&(P.value=(0,m.X)(u,this.options,y,this.speed)),g&&g(P.value),A&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return(0,r.X)(this.calculatedDuration)}get time(){return(0,r.X)(this.currentTime)}set time(t){t=(0,r.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(a.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,r.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=u,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,o.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},600:(t,e,i)=>{i.d(e,{e:()=>n});function n(t,{style:e,vars:i},n,s){for(let r in Object.assign(t.style,e,s&&s.getProjectionStyles(n)),i)t.style.setProperty(r,i[r])}},614:(t,e,i)=>{i.d(e,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},637:(t,e,i)=>{i.d(e,{$:()=>r,H:()=>s});var n=i(8606);let s={};function r(t){for(let e in t)s[e]=t[e],(0,n.j)(e)&&(s[e].isCSSVariable=!0)}},728:(t,e,i)=>{i.d(e,{l:()=>p});var n=i(18),s=i(1834),r=i(1786),a=i(5193),o=i(8450),l=i(2076);let h=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var u=i(3095),d=i(600),c=i(4527);class p extends a.b{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=r.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(n.f.has(e)){let t=(0,s.D)(e);return t&&t.default||0}return e=h.has(e)?e:(0,o.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,c.x)(t,e,i)}build(t,e,i){(0,l.B)(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in(0,d.e)(t,e,void 0,n),e.attrs)t.setAttribute(h.has(i)?i:(0,o.I)(i),e.attrs[i])}mount(t){this.isSVGTag=(0,u.n)(t.tagName),super.mount(t)}}},845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1081:(t,e,i)=>{i.d(e,{h:()=>n});let n=t=>Array.isArray(t)&&"number"!=typeof t[0]},1297:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>i>e?e:i<t?t:i},1335:(t,e,i)=>{i.d(e,{u:()=>s});var n=i(9064);let s={test:(0,i(5920).$)("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:n.B.transform}},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1513:(t,e,i)=>{i.d(e,{q:()=>n});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},1557:(t,e,i)=>{i.d(e,{a:()=>n});let n=t=>Math.round(1e5*t)/1e5},1765:(t,e,i)=>{i.d(e,{V:()=>n});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},1786:(t,e,i)=>{i.d(e,{ge:()=>a,xU:()=>s});let n=()=>({translate:0,scale:1,origin:0,originPoint:0}),s=()=>({x:n(),y:n()}),r=()=>({min:0,max:0}),a=()=>({x:r(),y:r()})},1788:(t,e,i)=>{i.d(e,{n:()=>n});let n="data-"+(0,i(8450).I)("framerAppearId")},1834:(t,e,i)=>{i.d(e,{D:()=>a});var n=i(4272),s=i(2171);let r={...i(2403).W,color:n.y,backgroundColor:n.y,outlineColor:n.y,fill:n.y,stroke:n.y,borderColor:n.y,borderTopColor:n.y,borderRightColor:n.y,borderBottomColor:n.y,borderLeftColor:n.y,filter:s.p,WebkitFilter:s.p},a=t=>r[t]},2017:(t,e,i)=>{i.d(e,{f:()=>r});var n=i(5818),s=i(3210);function r(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let a=(0,n.q)(0,e,r);t.push((0,s.k)(i,1,a))}}},2039:(t,e,i)=>{i.d(e,{a6:()=>s,am:()=>a,vT:()=>r});var n=i(2483);let s=(0,n.A)(.42,0,1,1),r=(0,n.A)(0,0,.58,1),a=(0,n.A)(.42,0,.58,1)},2076:(t,e,i)=>{i.d(e,{B:()=>o});var n=i(7684),s=i(4158);let r={offset:"stroke-dashoffset",array:"stroke-dasharray"},a={offset:"strokeDashoffset",array:"strokeDasharray"};function o(t,{attrX:e,attrY:i,attrScale:o,pathLength:l,pathSpacing:h=1,pathOffset:u=0,...d},c,p,m){if((0,n.O)(t,d,p),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:f,style:v}=t;f.transform&&(v.transform=f.transform,delete f.transform),(v.transform||f.transformOrigin)&&(v.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),v.transform&&(v.transformBox=m?.transformBox??"fill-box",delete f.transformBox),void 0!==e&&(f.x=e),void 0!==i&&(f.y=i),void 0!==o&&(f.scale=o),void 0!==l&&function(t,e,i=1,n=0,o=!0){t.pathLength=1;let l=o?r:a;t[l.offset]=s.px.transform(-n);let h=s.px.transform(e),u=s.px.transform(i);t[l.array]=`${h} ${u}`}(f,l,h,u,!1)}},2082:(t,e,i)=>{i.d(e,{xQ:()=>r});var n=i(2115),s=i(845);function r(t=!0){let e=(0,n.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(l)},[t]);let h=(0,n.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,h]:[!0]}},2171:(t,e,i)=>{i.d(e,{p:()=>l});var n=i(10),s=i(614);let r=new Set(["brightness","contrast","saturate","opacity"]);function a(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(s.S)||[];if(!n)return t;let a=i.replace(n,""),o=+!!r.has(e);return n!==i&&(o*=100),e+"("+o+a+")"}let o=/\b([a-z-]*)\(.*?\)/gu,l={...n.f,getAnimatableNone:t=>{let e=t.match(o);return e?e.map(a).join(" "):t}}},2198:(t,e,i)=>{i.d(e,{K:()=>n});function n(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let s=i?.[t]??n.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}},2403:(t,e,i)=>{i.d(e,{W:()=>o});var n=i(7887);let s={...n.ai,transform:Math.round};var r=i(4158);let a={rotate:r.uj,rotateX:r.uj,rotateY:r.uj,rotateZ:r.uj,scale:n.hs,scaleX:n.hs,scaleY:n.hs,scaleZ:n.hs,skew:r.uj,skewX:r.uj,skewY:r.uj,distance:r.px,translateX:r.px,translateY:r.px,translateZ:r.px,x:r.px,y:r.px,z:r.px,perspective:r.px,transformPerspective:r.px,opacity:n.X4,originX:r.gQ,originY:r.gQ,originZ:r.px},o={borderWidth:r.px,borderTopWidth:r.px,borderRightWidth:r.px,borderBottomWidth:r.px,borderLeftWidth:r.px,borderRadius:r.px,radius:r.px,borderTopLeftRadius:r.px,borderTopRightRadius:r.px,borderBottomRightRadius:r.px,borderBottomLeftRadius:r.px,width:r.px,maxWidth:r.px,height:r.px,maxHeight:r.px,top:r.px,right:r.px,bottom:r.px,left:r.px,padding:r.px,paddingTop:r.px,paddingRight:r.px,paddingBottom:r.px,paddingLeft:r.px,margin:r.px,marginTop:r.px,marginRight:r.px,marginBottom:r.px,marginLeft:r.px,backgroundPositionX:r.px,backgroundPositionY:r.px,...a,zIndex:s,fillOpacity:n.X4,strokeOpacity:n.X4,numOctaves:s}},2458:(t,e,i)=>{i.d(e,{Y:()=>n,t:()=>s});let n=2e4;function s(t){let e=0,i=t.next(e);for(;!i.done&&e<n;)e+=50,i=t.next(e);return e>=n?1/0:e}},2483:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(9827);let s=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function r(t,e,i,r){if(t===e&&i===r)return n.l;let a=e=>(function(t,e,i,n,r){let a,o,l=0;do(a=s(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(a)>1e-7&&++l<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:s(a(t),e,r)}},2662:(t,e,i)=>{function n(t){return void 0===t||1===t}function s({scale:t,scaleX:e,scaleY:i}){return!n(t)||!n(e)||!n(i)}function r(t){return s(t)||a(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function a(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>r,vF:()=>a,vk:()=>s})},2735:(t,e,i)=>{function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function s(t,e,i,s){if("function"==typeof e){let[r,a]=n(s);e=e(void 0!==i?i:t.custom,r,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,a]=n(s);e=e(void 0!==i?i:t.custom,r,a)}return e}i.d(e,{a:()=>s})},2885:(t,e,i)=>{i.d(e,{M:()=>s});var n=i(2115);function s(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2923:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}},3014:(t,e,i)=>{i.d(e,{i:()=>n});let n=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t)},3055:(t,e,i)=>{i.d(e,{z:()=>r});var n=i(18),s=i(637);function r(t,{layout:e,layoutId:i}){return n.f.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!s.H[t]||"opacity"===t)}},3095:(t,e,i)=>{i.d(e,{n:()=>n});let n=t=>"string"==typeof t&&"svg"===t.toLowerCase()},3128:(t,e,i)=>{i.d(e,{W:()=>n});function n(t){return"function"==typeof t&&"applyToOptions"in t}},3191:(t,e,i)=>{i.d(e,{F:()=>s});let n=(t,e)=>i=>e(t(i)),s=(...t)=>t.reduce(n)},3210:(t,e,i)=>{i.d(e,{k:()=>n});let n=(t,e,i)=>t+(e-t)*i},3387:(t,e,i)=>{i.d(e,{W:()=>n});let n={}},3522:(t,e,i)=>{i.d(e,{w:()=>n});let n=t=>e=>e.test(t)},3562:(t,e,i)=>{i.d(e,{B:()=>k});var n=i(7322),s=i(4261),r=i(9515),a=i(4803),o=i(18),l=i(98),h=i(4272),u=i(10),d=i(4050),c=i(3522);let p=[...d.T,h.y,u.f],m=t=>p.find((0,c.w)(t));var f=i(7277),v=i(3014),g=i(7312),y=i(5626),x=i(6642),T=i(1786),w=i(8972);let b={current:null},P={current:!1};var S=i(5511),A=i(9253),V=i(2735);let M=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class k{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:o,blockInitialAnimation:l,visualState:h},u={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=n.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=s.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,r.Gt.render(this.render,!1,!0))};let{latestValues:d,renderState:c}=h;this.latestValues=d,this.baseTarget={...d},this.initialValues=e.initial?{...d}:{},this.renderState=c,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=u,this.blockInitialAnimation=!!l,this.isControllingVariants=(0,A.e)(e),this.isVariantNode=(0,A.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:p,...m}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in m){let e=m[t];void 0!==d[t]&&(0,a.S)(e)&&e.set(d[t],!1)}}mount(t){this.current=t,S.C.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),P.current||function(){if(P.current=!0,w.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>b.current=t.matches;t.addListener(e),e()}else b.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||b.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,r.WG)(this.notifyUpdate),(0,r.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=o.f.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&r.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),a(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in x.B){let e=x.B[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,T.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<M.length;e++){let i=M[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if((0,a.S)(s))t.addValue(n,s);else if((0,a.S)(r))t.addValue(n,(0,l.OQ)(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,(0,l.OQ)(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,l.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&((0,v.i)(i)||(0,g.$)(i))?i=parseFloat(i):!m(i)&&u.f.test(e)&&(i=(0,f.J)(t,e)),this.setBaseTarget(t,(0,a.S)(i)?i.get():i)),(0,a.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=(0,V.a)(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||(0,a.S)(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new y.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}},3704:(t,e,i)=>{i.d(e,{q:()=>n});let n={layout:0,mainThread:0,waapi:0}},3757:(t,e,i)=>{i.d(e,{L:()=>a,m:()=>r});var n=i(8588),s=i(6147);function r(t,e){return(0,n.FY)((0,n.bS)(t.getBoundingClientRect(),e))}function a(t,e,i){let n=r(t,i),{scroll:a}=e;return a&&((0,s.Ql)(n.x,a.offset.x),(0,s.Ql)(n.y,a.offset.y)),n}},3945:(t,e,i)=>{i.d(e,{Y:()=>s});var n=i(2923);function s(t,e,i){let s=Math.max(e-5,0);return(0,n.f)(i-t(s),e-s)}},3972:(t,e,i)=>{i.d(e,{Sz:()=>a,ZZ:()=>l,dg:()=>o});var n=i(2483),s=i(1765),r=i(4180);let a=(0,n.A)(.33,1.53,.69,.99),o=(0,r.G)(a),l=(0,s.V)(o)},4050:(t,e,i)=>{i.d(e,{T:()=>a,n:()=>o});var n=i(7887),s=i(4158),r=i(3522);let a=[n.ai,s.px,s.KN,s.uj,s.vw,s.vh,{test:t=>"auto"===t,parse:t=>t}],o=t=>a.find((0,r.w)(t))},4158:(t,e,i)=>{i.d(e,{KN:()=>r,gQ:()=>h,px:()=>a,uj:()=>s,vh:()=>o,vw:()=>l});let n=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),s=n("deg"),r=n("%"),a=n("px"),o=n("vh"),l=n("vw"),h={...r,parse:t=>r.parse(t)/100,transform:t=>r.transform(100*t)}},4160:(t,e,i)=>{i.d(e,{Ib:()=>c,ry:()=>d,zs:()=>u});let n=t=>180*t/Math.PI,s=t=>a(n(Math.atan2(t[1],t[0]))),r={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:s,rotateZ:s,skewX:t=>n(Math.atan(t[1])),skewY:t=>n(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},a=t=>((t%=360)<0&&(t+=360),t),o=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),l=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),h={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:o,scaleY:l,scale:t=>(o(t)+l(t))/2,rotateX:t=>a(n(Math.atan2(t[6],t[5]))),rotateY:t=>a(n(Math.atan2(-t[2],t[0]))),rotateZ:s,rotate:s,skewX:t=>n(Math.atan(t[4])),skewY:t=>n(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function u(t){return+!!t.includes("scale")}function d(t,e){let i,n;if(!t||"none"===t)return u(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=h,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=r,n=e}if(!n)return u(e);let a=i[e],o=n[1].split(",").map(p);return"function"==typeof a?a(o):o[a]}let c=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return d(i,e)};function p(t){return parseFloat(t.trim())}},4180:(t,e,i)=>{i.d(e,{G:()=>n});let n=t=>e=>1-t(1-e)},4261:(t,e,i)=>{let n;i.d(e,{k:()=>o});var s=i(3387),r=i(9515);function a(){n=void 0}let o={now:()=>(void 0===n&&o.set(r.uv.isProcessing||s.W.useManualTiming?r.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(a)}}},4272:(t,e,i)=>{i.d(e,{y:()=>a});var n=i(1335),s=i(8476),r=i(9064);let a={test:t=>r.B.test(t)||n.u.test(t)||s.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):s.V.test(t)?s.V.parse(t):n.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):s.V.transform(t),getAnimatableNone:t=>{let e=a.parse(t);return e.alpha=0,a.transform(e)}}},4527:(t,e,i)=>{i.d(e,{x:()=>a});var n=i(4803),s=i(18),r=i(8609);function a(t,e,i){let a=(0,r.x)(t,e,i);for(let i in t)((0,n.S)(t[i])||(0,n.S)(e[i]))&&(a[-1!==s.U.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return a}},4542:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>s});let n=()=>{},s=()=>{}},4608:(t,e,i)=>{i.d(e,{X:()=>r});var n=i(7215),s=i(2458);function r(t,e=100,i){let a=i({...t,keyframes:[0,e]}),o=Math.min((0,s.t)(a),s.Y);return{type:"keyframes",ease:t=>a.next(o*t).value/e,duration:(0,n.X)(o)}}},4687:(t,e,i)=>{i.d(e,{o:()=>f});var n=i(1297),s=i(7215),r=i(7705),a=i(2458),o=i(4608),l=i(3945);let h={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var u=i(4542);function d(t,e){return t*Math.sqrt(1-e*e)}let c=["duration","bounce"],p=["stiffness","damping","mass"];function m(t,e){return e.some(e=>void 0!==t[e])}function f(t=h.visualDuration,e=h.bounce){let i,o="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:v,restDelta:g}=o,y=o.keyframes[0],x=o.keyframes[o.keyframes.length-1],T={done:!1,value:y},{stiffness:w,damping:b,mass:P,duration:S,velocity:A,isResolvedFromDuration:V}=function(t){let e={velocity:h.velocity,stiffness:h.stiffness,damping:h.damping,mass:h.mass,isResolvedFromDuration:!1,...t};if(!m(t,p)&&m(t,c))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,r=2*(0,n.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:h.mass,stiffness:s,damping:r}}else{let i=function({duration:t=h.duration,bounce:e=h.bounce,velocity:i=h.velocity,mass:r=h.mass}){let a,o;(0,u.$)(t<=(0,s.f)(h.maxDuration),"Spring duration must be 10 seconds or less");let l=1-e;l=(0,n.q)(h.minDamping,h.maxDamping,l),t=(0,n.q)(h.minDuration,h.maxDuration,(0,s.X)(t)),l<1?(a=e=>{let n=e*l,s=n*t;return .001-(n-i)/d(e,l)*Math.exp(-s)},o=e=>{let n=e*l*t,s=Math.pow(l,2)*Math.pow(e,2)*t,r=Math.exp(-n),o=d(Math.pow(e,2),l);return(n*i+i-s)*r*(-a(e)+.001>0?-1:1)/o}):(a=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),o=e=>t*t*(i-e)*Math.exp(-e*t));let c=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(a,o,5/t);if(t=(0,s.f)(t),isNaN(c))return{stiffness:h.stiffness,damping:h.damping,duration:t};{let e=Math.pow(c,2)*r;return{stiffness:e,damping:2*l*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:h.mass}).isResolvedFromDuration=!0}return e}({...o,velocity:-(0,s.X)(o.velocity||0)}),M=A||0,k=b/(2*Math.sqrt(w*P)),E=x-y,D=(0,s.X)(Math.sqrt(w/P)),C=5>Math.abs(E);if(v||(v=C?h.restSpeed.granular:h.restSpeed.default),g||(g=C?h.restDelta.granular:h.restDelta.default),k<1){let t=d(D,k);i=e=>x-Math.exp(-k*D*e)*((M+k*D*E)/t*Math.sin(t*e)+E*Math.cos(t*e))}else if(1===k)i=t=>x-Math.exp(-D*t)*(E+(M+D*E)*t);else{let t=D*Math.sqrt(k*k-1);i=e=>{let i=Math.exp(-k*D*e),n=Math.min(t*e,300);return x-i*((M+k*D*E)*Math.sinh(n)+t*E*Math.cosh(n))/t}}let R={calculatedDuration:V&&S||null,next:t=>{let e=i(t);if(V)T.done=t>=S;else{let n=0===t?M:0;k<1&&(n=0===t?(0,s.f)(M):(0,l.Y)(i,t,e));let r=Math.abs(x-e)<=g;T.done=Math.abs(n)<=v&&r}return T.value=T.done?x:e,T},toString:()=>{let t=Math.min((0,a.t)(R),a.Y),e=(0,r.K)(e=>R.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return R}f.applyToOptions=t=>{let e=(0,o.X)(t,100,f);return t.ease=e.ease,t.duration=(0,s.f)(e.duration),t.type="keyframes",t}},4744:(t,e,i)=>{i.d(e,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},4749:(t,e,i)=>{i.d(e,{K:()=>p});var n=i(4542),s=i(9827),r=i(6009),a=i(3972),o=i(7712),l=i(2483),h=i(2039),u=i(8589);let d={linear:s.l,easeIn:h.a6,easeInOut:h.am,easeOut:h.vT,circIn:o.po,circInOut:o.tn,circOut:o.yT,backIn:a.dg,backInOut:a.ZZ,backOut:a.Sz,anticipate:r.b},c=t=>"string"==typeof t,p=t=>{if((0,u.D)(t)){(0,n.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,r]=t;return(0,l.A)(e,i,s,r)}return c(t)?((0,n.V)(void 0!==d[t],`Invalid easing type '${t}'`),d[t]):t}},4803:(t,e,i)=>{i.d(e,{S:()=>n});let n=t=>!!(t&&t.getVelocity)},5193:(t,e,i)=>{i.d(e,{b:()=>y});var n=i(8109),s=i(4050),r=i(4542),a=i(3014),o=i(8606);let l=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var h=i(7322),u=i(7312),d=i(10),c=i(7277);let p=new Set(["auto","none","0"]);var m=i(280);class f extends h.h{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,o.p)(n))){let s=function t(e,i,n=1){(0,r.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,h]=function(t){let e=l.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let u=window.getComputedStyle(i).getPropertyValue(s);if(u){let t=u.trim();return(0,a.i)(t)?parseFloat(t):t}return(0,o.p)(h)?t(h,i,n+1):h}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!n.$.has(i)||2!==t.length)return;let[h,u]=t,d=(0,s.n)(h),c=(0,s.n)(u);if(d!==c)if((0,m.E4)(d)&&(0,m.E4)(c))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else m.Hr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||(0,u.$)(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!p.has(e)&&(0,d.V)(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=(0,c.J)(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=m.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=m.Hr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}var v=i(4803),g=i(3562);class y extends g.B{constructor(){super(...arguments),this.KeyframeResolver=f}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,v.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}},5245:(t,e,i)=>{i.d(e,{M:()=>d});var n=i(18),s=i(4160),r=i(8606),a=i(3757),o=i(5193),l=i(7684),h=i(600),u=i(8609);class d extends o.b{constructor(){super(...arguments),this.type="html",this.renderInstance=h.e}readValueFromInstance(t,e){if(n.f.has(e))return this.projection?.isProjecting?(0,s.zs)(e):(0,s.Ib)(t,e);{let i=window.getComputedStyle(t),n=((0,r.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,a.m)(t,e)}build(t,e,i){(0,l.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,u.x)(t,e,i)}}},5305:(t,e,i)=>{i.d(e,{w:()=>n});function n(t){return"string"==typeof t||Array.isArray(t)}},5511:(t,e,i)=>{i.d(e,{C:()=>n});let n=new WeakMap},5580:(t,e,i)=>{i.d(e,{z:()=>a});var n=i(4803),s=i(98),r=i(6335);function a(t,e,i){let a=(0,n.S)(t)?t:(0,s.OQ)(t);return a.start((0,r.f)("",a,e,i)),a.animation}},5626:(t,e,i)=>{i.d(e,{v:()=>s});var n=i(6668);class s{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},5818:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},5910:(t,e,i)=>{i.d(e,{p:()=>n});let n=t=>Array.isArray(t)},5920:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>a});var n=i(614);let s=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&s.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[r,a,o,l]=s.match(n.S);return{[t]:parseFloat(r),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},5943:(t,e,i)=>{i.d(e,{h:()=>s});var n=i(9782);function s(t){return(0,n.x)(t)&&"svg"===t.tagName}},6009:(t,e,i)=>{i.d(e,{b:()=>s});var n=i(3972);let s=t=>(t*=2)<1?.5*(0,n.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},6087:(t,e,i)=>{i.d(e,{j:()=>A});var n=i(3191),s=i(4542),r=i(8606),a=i(4272),o=i(10),l=i(1335),h=i(8476);function u(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var d=i(9064);function c(t,e){return i=>i>0?e:t}var p=i(3210);let m=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},f=[l.u,d.B,h.V],v=t=>f.find(e=>e.test(t));function g(t){let e=v(t);if((0,s.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===h.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=u(o,n,t+1/3),r=u(o,n,t),a=u(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let y=(t,e)=>{let i=g(t),n=g(e);if(!i||!n)return c(t,e);let s={...i};return t=>(s.red=m(i.red,n.red,t),s.green=m(i.green,n.green,t),s.blue=m(i.blue,n.blue,t),s.alpha=(0,p.k)(i.alpha,n.alpha,t),d.B.transform(s))},x=new Set(["none","hidden"]);function T(t,e){return i=>(0,p.k)(t,e,i)}function w(t){return"number"==typeof t?T:"string"==typeof t?(0,r.p)(t)?c:a.y.test(t)?y:S:Array.isArray(t)?b:"object"==typeof t?a.y.test(t)?y:P:c}function b(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function P(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=w(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let S=(t,e)=>{let i=o.f.createTransformer(e),r=(0,o.V)(t),a=(0,o.V)(e);return r.indexes.var.length===a.indexes.var.length&&r.indexes.color.length===a.indexes.color.length&&r.indexes.number.length>=a.indexes.number.length?x.has(t)&&!a.values.length||x.has(e)&&!r.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,n.F)(b(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(r,a),a.values),i):((0,s.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),c(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):w(t)(t,e)}},6147:(t,e,i)=>{i.d(e,{OU:()=>h,Ql:()=>u,Ww:()=>c,hq:()=>r,o4:()=>l});var n=i(3210),s=i(2662);function r(t,e,i){return i+e*(t-i)}function a(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function o(t,e=0,i=1,n,s){t.min=a(t.min,e,i,n,s),t.max=a(t.max,e,i,n,s)}function l(t,{x:e,y:i}){o(t.x,e.translate,e.scale,e.originPoint),o(t.y,i.translate,i.scale,i.originPoint)}function h(t,e,i,n=!1){let r,a,o=i.length;if(o){e.x=e.y=1;for(let h=0;h<o;h++){a=(r=i[h]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&c(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,l(t,a)),n&&(0,s.HD)(r.latestValues)&&c(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function u(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,s,r=.5){let a=(0,n.k)(t.min,t.max,r);o(t,e,i,a,s)}function c(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},6330:(t,e,i)=>{i.d(e,{B:()=>r});var n=i(4687),s=i(3945);function r({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:a=10,bounceStiffness:o=500,modifyTarget:l,min:h,max:u,restDelta:d=.5,restSpeed:c}){let p,m,f=t[0],v={done:!1,value:f},g=t=>void 0!==h&&t<h||void 0!==u&&t>u,y=t=>void 0===h?u:void 0===u||Math.abs(h-t)<Math.abs(u-t)?h:u,x=i*e,T=f+x,w=void 0===l?T:l(T);w!==T&&(x=w-f);let b=t=>-x*Math.exp(-t/r),P=t=>w+b(t),S=t=>{let e=b(t),i=P(t);v.done=Math.abs(e)<=d,v.value=v.done?w:i},A=t=>{g(v.value)&&(p=t,m=(0,n.o)({keyframes:[v.value,y(v.value)],velocity:(0,s.Y)(P,t,v.value),damping:a,stiffness:o,restDelta:d,restSpeed:c}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==p||(e=!0,S(t),A(t)),void 0!==p&&t>=p)?m.next(t-p):(e||S(t),v)}}}},6333:(t,e,i)=>{i.d(e,{g:()=>r});var n=i(3387),s=i(4803);function r(t,e){let i=t.getValue("willChange");if((0,s.S)(i)&&i.add)return i.add(e);if(!i&&n.W.WillChange){let i=new n.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}},6335:(t,e,i)=>{i.d(e,{f:()=>K});var n=i(8777),s=i(9515),r=i(532),a=i(3387),o=i(9827),l=i(4261),h=i(6778),u=i(7322),d=i(7215),c=i(4542);let p=t=>t.startsWith("--");function m(t){let e;return()=>(void 0===e&&(e=t()),e)}let f=m(()=>void 0!==window.ScrollTimeline);var v=i(1513),g=i(3704),y=i(4744),x=i(8589);let T={},w=function(t,e){let i=m(t);return()=>T[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var b=i(7705);let P=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,S={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:P([0,.65,.55,1]),circOut:P([.55,0,1,.45]),backIn:P([.31,.01,.66,-.59]),backOut:P([.33,1.53,.69,.99])};var A=i(3128);class V extends v.q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,(0,c.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return(0,A.W)(t)&&w()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?w()?(0,b.K)(e,i):"ease-out":(0,x.D)(e)?P(e):Array.isArray(e)?e.map(e=>t(e,i)||S.easeOut):S[e]}(o,s);Array.isArray(d)&&(u.easing=d),y.Q.value&&g.q.waapi++;let c={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return y.Q.value&&p.finished.finally(()=>{g.q.waapi--}),p}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=(0,h.X)(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){p(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,d.X)(Number(t))}get time(){return(0,d.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,d.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&f())?(this.animation.timeline=t,o.l):e(this)}}var M=i(144),k=i(6009),E=i(3972),D=i(7712);let C={anticipate:k.b,backInOut:E.ZZ,circInOut:D.tn};class R extends V{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in C&&(t.ease=C[t.ease])}(t),(0,M.E)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...a}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new r.s({...a,autoplay:!1}),l=(0,d.f)(this.finishedTime??this.time);e.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}var j=i(10);let L=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(j.f.test(t)||"0"===t)&&!t.startsWith("url("));var B=i(7351);let F=new Set(["opacity","clipPath","filter","transform"]),O=m(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class I extends v.q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:h,element:d,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=l.k.now();let p={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:h,element:d,...c},m=d?.KeyframeResolver||u.h;this.keyframeResolver=new m(a,(t,e,i)=>this.onKeyframesResolved(t,e,p,!i),o,h,d),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:u,velocity:d,delay:p,isHandoff:m,onUpdate:f}=i;this.resolvedAt=l.k.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=L(s,e),o=L(r,e);return(0,c.$)(a===o,`You are trying to animate ${e} from "${s}" to "${r}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||(0,A.W)(i))&&n)}(t,s,u,d)&&((a.W.instantAnimations||!p)&&f?.((0,h.X)(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let v={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},g=!m&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!(0,B.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return O()&&i&&F.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(v)?new R({...v,element:v.motionValue.owner.current}):new r.s(v);g.finished.then(()=>this.notifyFinished()).catch(o.l),this.pendingTimeline&&(this.stopTimeline=g.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=g}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,u.q)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let U=t=>null!==t;var W=i(18);let $={type:"spring",stiffness:500,damping:25,restSpeed:10},N=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),q={type:"keyframes",duration:.8},G={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},X=(t,{keyframes:e})=>e.length>2?q:W.f.has(t)?t.startsWith("scale")?N(e[1]):$:G,K=(t,e,i,o={},l,h)=>u=>{let c=(0,n.r)(o,t)||{},p=c.delay||o.delay||0,{elapsed:m=0}=o;m-=(0,d.f)(p);let f={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...c,delay:-m,onUpdate:t=>{e.set(t),c.onUpdate&&c.onUpdate(t)},onComplete:()=>{u(),c.onComplete&&c.onComplete()},name:t,motionValue:e,element:h?void 0:l};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(c)&&Object.assign(f,X(t,f)),f.duration&&(f.duration=(0,d.f)(f.duration)),f.repeatDelay&&(f.repeatDelay=(0,d.f)(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let v=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0===f.delay&&(v=!0)),(a.W.instantAnimations||a.W.skipAnimations)&&(v=!0,f.duration=0,f.delay=0),f.allowFlatten=!c.type&&!c.ease,v&&!h&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(U),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(f.keyframes,c);if(void 0!==t)return void s.Gt.update(()=>{f.onUpdate(t),f.onComplete()})}return c.isSync?new r.s(f):new I(f)}},6340:(t,e,i)=>{i.d(e,{N:()=>n});function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},6642:(t,e,i)=>{i.d(e,{B:()=>s});let n={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},s={};for(let t in n)s[t]={isEnabled:e=>n[t].some(t=>!!e[t])}},6668:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function s(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>s,Kq:()=>n})},6775:(t,e,i)=>{i.d(e,{G:()=>u});var n=i(3387),s=i(9827),r=i(3191),a=i(4542),o=i(5818),l=i(1297),h=i(6087);function u(t,e,{clamp:i=!0,ease:d,mixer:c}={}){let p=t.length;if((0,a.V)(p===e.length,"Both input and output ranges must be the same length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let m=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let f=function(t,e,i){let a=[],o=i||n.W.mix||h.j,l=t.length-1;for(let i=0;i<l;i++){let n=o(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||s.l:e;n=(0,r.F)(t,n)}a.push(n)}return a}(e,d,c),v=f.length,g=i=>{if(m&&i<t[0])return e[0];let n=0;if(v>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=(0,o.q)(t[n],t[n+1],i);return f[n](s)};return i?e=>g((0,l.q)(t[0],t[p-1],e)):g}},6778:(t,e,i)=>{i.d(e,{X:()=>s});let n=t=>null!==t;function s(t,{repeat:e,repeatType:i="loop"},r,a=1){let o=t.filter(n),l=a<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return l&&void 0!==r?r:o[l]}},6926:(t,e,i)=>{i.d(e,{P:()=>s});var n=i(1788);function s(t){return t.props[n.n]}},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7215:(t,e,i)=>{i.d(e,{X:()=>s,f:()=>n});let n=t=>1e3*t,s=t=>t/1e3},7277:(t,e,i)=>{i.d(e,{J:()=>a});var n=i(10),s=i(2171),r=i(1834);function a(t,e){let i=(0,r.D)(t);return i!==s.p&&(i=n.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}},7312:(t,e,i)=>{i.d(e,{$:()=>n});let n=t=>/^0[^.\s]+$/u.test(t)},7322:(t,e,i)=>{i.d(e,{h:()=>c,q:()=>d});var n=i(280),s=i(9515);let r=new Set,a=!1,o=!1,l=!1;function h(){if(o){let t=Array.from(r).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,n.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}o=!1,a=!1,r.forEach(t=>t.complete(l)),r.clear()}function u(){r.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(o=!0)})}function d(){l=!0,u(),h(),l=!1}class c{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(r.add(this),a||(a=!0,s.Gt.read(u),s.Gt.resolveKeyframes(h))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),r.delete(this)}cancel(){"scheduled"===this.state&&(r.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}},7351:(t,e,i)=>{i.d(e,{s:()=>s});var n=i(6983);function s(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>s});var n=i(2115);let s=i(8972).B?n.useLayoutEffect:n.useEffect},7684:(t,e,i)=>{i.d(e,{O:()=>h});var n=i(18),s=i(8606);let r=(t,e)=>e&&"number"==typeof t?e.transform(t):t;var a=i(2403);let o={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},l=n.U.length;function h(t,e,i){let{style:h,vars:u,transformOrigin:d}=t,c=!1,p=!1;for(let t in e){let i=e[t];if(n.f.has(t)){c=!0;continue}if((0,s.j)(t)){u[t]=i;continue}{let e=r(i,a.W[t]);t.startsWith("origin")?(p=!0,d[t]=e):h[t]=e}}if(!e.transform&&(c||i?h.transform=function(t,e,i){let s="",h=!0;for(let u=0;u<l;u++){let l=n.U[u],d=t[l];if(void 0===d)continue;let c=!0;if(!(c="number"==typeof d?d===+!!l.startsWith("scale"):0===parseFloat(d))||i){let t=r(d,a.W[l]);if(!c){h=!1;let e=o[l]||l;s+=`${e}(${t}) `}i&&(e[l]=t)}}return s=s.trim(),i?s=i(e,h?"":s):h&&(s="none"),s}(e,t.transform,i):h.transform&&(h.transform="none")),p){let{originX:t="50%",originY:e="50%",originZ:i=0}=d;h.transformOrigin=`${t} ${e} ${i}`}}},7705:(t,e,i)=>{i.d(e,{K:()=>n});let n=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`}},7712:(t,e,i)=>{i.d(e,{po:()=>r,tn:()=>o,yT:()=>a});var n=i(1765),s=i(4180);let r=t=>1-Math.sin(Math.acos(t)),a=(0,s.G)(r),o=(0,n.V)(r)},7887:(t,e,i)=>{i.d(e,{X4:()=>r,ai:()=>s,hs:()=>a});var n=i(1297);let s={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},r={...s,transform:t=>(0,n.q)(0,1,t)},a={...s,default:1}},7934:(t,e,i)=>{i.d(e,{$:()=>c});var n=i(8777),s=i(9515),r=i(8109),a=i(98),o=i(5910),l=i(419),h=i(6333),u=i(6926),d=i(6335);function c(t,e,{delay:i=0,transitionOverride:p,type:m}={}){let{transition:f=t.getDefaultTransition(),transitionEnd:v,...g}=e;p&&(f=p);let y=[],x=m&&t.animationState&&t.animationState.getState()[m];for(let e in g){let a=t.getValue(e,t.latestValues[e]??null),o=g[e];if(void 0===o||x&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(x,e))continue;let l={delay:i,...(0,n.r)(f||{},e)},c=a.get();if(void 0!==c&&!a.isAnimating&&!Array.isArray(o)&&o===c&&!l.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let i=(0,u.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,s.Gt);null!==t&&(l.startTime=t,p=!0)}}(0,h.g)(t,e),a.start((0,d.f)(e,a,o,t.shouldReduceMotion&&r.$.has(e)?{type:!1}:l,t,p));let m=a.animation;m&&y.push(m)}return v&&Promise.all(y).then(()=>{s.Gt.update(()=>{v&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=(0,l.K)(t,e)||{};for(let e in s={...s,...i}){var r;let i=(r=s[e],(0,o.p)(r)?r[r.length-1]||0:r);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,a.OQ)(i))}}(t,v)})}),y}},8109:(t,e,i)=>{i.d(e,{$:()=>n});let n=new Set(["width","height","top","left","right","bottom",...i(18).U])},8312:(t,e,i)=>{i.d(e,{U:()=>n,_:()=>s});let n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],s=["initial",...n]},8437:(t,e,i)=>{i.d(e,{I:()=>a});var n=i(3387);let s=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var r=i(4744);function a(t,e){let i=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,h=s.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,a=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},h=0;function u(e){o.has(e)&&(d.schedule(e),t()),h++,e(l)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&s?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(l=t,s){a=!0;return}s=!0,[i,n]=[n,i],i.forEach(u),e&&r.Q.value&&r.Q.value.frameloop[e].push(h),h=0,i.clear(),s=!1,a&&(a=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:u,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:v,postRender:g}=h,y=()=>{let s=n.W.useManualTiming?o.timestamp:performance.now();i=!1,n.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(s-o.timestamp,40),1)),o.timestamp=s,o.isProcessing=!0,u.process(o),d.process(o),c.process(o),p.process(o),m.process(o),f.process(o),v.process(o),g.process(o),o.isProcessing=!1,i&&e&&(a=!1,t(y))},x=()=>{i=!0,a=!0,o.isProcessing||t(y)};return{schedule:s.reduce((t,e)=>{let n=h[e];return t[e]=(t,e=!1,s=!1)=>(i||x(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<s.length;e++)h[s[e]].cancel(t)},state:o,steps:h}}},8450:(t,e,i)=>{i.d(e,{I:()=>n});let n=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},8476:(t,e,i)=>{i.d(e,{V:()=>o});var n=i(7887),s=i(4158),r=i(1557),a=i(5920);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+s.KN.transform((0,r.a)(e))+", "+s.KN.transform((0,r.a)(i))+", "+(0,r.a)(n.X4.transform(a))+")"}},8498:(t,e,i)=>{i.d(e,{i:()=>l});var n=i(2039),s=i(1081),r=i(4749),a=i(6775),o=i(9115);function l({duration:t=300,keyframes:e,times:i,ease:l="easeInOut"}){var h;let u=(0,s.h)(l)?l.map(r.K):(0,r.K)(l),d={done:!1,value:e[0]},c=(h=i&&i.length===e.length?i:(0,o.Z)(e),h.map(e=>e*t)),p=(0,a.G)(c,e,{ease:Array.isArray(u)?u:e.map(()=>u||n.am).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(d.value=p(e),d.done=e>=t,d)}}},8588:(t,e,i)=>{function n({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function s({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function r(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}i.d(e,{FY:()=>n,bS:()=>r,pA:()=>s})},8589:(t,e,i)=>{i.d(e,{D:()=>n});let n=t=>Array.isArray(t)&&"number"==typeof t[0]},8606:(t,e,i)=>{i.d(e,{j:()=>s,p:()=>a});let n=t=>e=>"string"==typeof e&&e.startsWith(t),s=n("--"),r=n("var(--"),a=t=>!!r(t)&&o.test(t.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},8609:(t,e,i)=>{i.d(e,{x:()=>r});var n=i(4803),s=i(3055);function r(t,e,i){let{style:r}=t,a={};for(let o in r)((0,n.S)(r[o])||e.style&&(0,n.S)(e.style[o])||(0,s.z)(o,t)||i?.getValue(o)?.liveStyle!==void 0)&&(a[o]=r[o]);return a}},8777:(t,e,i)=>{i.d(e,{r:()=>n});function n(t,e){return t?.[e]??t?.default??t}},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9064:(t,e,i)=>{i.d(e,{B:()=>h});var n=i(1297),s=i(7887),r=i(1557),a=i(5920);let o=t=>(0,n.q)(0,255,t),l={...s.ai,transform:t=>Math.round(o(t))},h={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,r.a)(s.X4.transform(n))+")"}},9115:(t,e,i)=>{i.d(e,{Z:()=>s});var n=i(2017);function s(t){let e=[0];return(0,n.f)(e,t.length-1),e}},9253:(t,e,i)=>{i.d(e,{O:()=>o,e:()=>a});var n=i(6340),s=i(5305),r=i(8312);function a(t){return(0,n.N)(t.animate)||r._.some(e=>(0,s.w)(t[e]))}function o(t){return!!(a(t)||t.variants)}},9515:(t,e,i)=>{i.d(e,{Gt:()=>s,PP:()=>o,WG:()=>r,uv:()=>a});var n=i(9827);let{schedule:s,cancel:r,state:a,steps:o}=(0,i(8437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},9782:(t,e,i)=>{i.d(e,{x:()=>s});var n=i(6983);function s(t){return(0,n.G)(t)&&"ownerSVGElement"in t}},9827:(t,e,i)=>{i.d(e,{l:()=>n});let n=t=>t}}]);