{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/text-generate-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { motion, stagger, useAnimate } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TextGenerateEffectProps {\n  words: string;\n  className?: string;\n  filter?: boolean;\n  duration?: number;\n}\n\nexport const TextGenerateEffect: React.FC<TextGenerateEffectProps> = ({\n  words,\n  className,\n  filter = true,\n  duration = 0.5,\n}) => {\n  const [scope, animate] = useAnimate();\n  const wordsArray = words.split(\" \");\n\n  useEffect(() => {\n    animate(\n      \"span\",\n      {\n        opacity: 1,\n        filter: filter ? \"blur(0px)\" : \"none\",\n      },\n      {\n        duration: duration ? duration : 1,\n        delay: stagger(0.2),\n      }\n    );\n  }, [scope.current, animate, duration, filter]);\n\n  const renderWords = () => {\n    return (\n      <motion.div ref={scope}>\n        {wordsArray.map((word, idx) => {\n          return (\n            <motion.span\n              key={word + idx}\n              className=\"dark:text-white text-black opacity-0\"\n              style={{\n                filter: filter ? \"blur(10px)\" : \"none\",\n              }}\n            >\n              {word}{\" \"}\n            </motion.span>\n          );\n        })}\n      </motion.div>\n    );\n  };\n\n  return (\n    <div className={cn(\"font-bold\", className)}>\n      <div className=\"mt-4\">\n        <div className=\"dark:text-white text-black text-2xl leading-snug tracking-wide\">\n          {renderWords()}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAaO,MAAM,qBAAwD,CAAC,EACpE,KAAK,EACL,SAAS,EACT,SAAS,IAAI,EACb,WAAW,GAAG,EACf;IACC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,uLAAA,CAAA,aAAU,AAAD;IAClC,MAAM,aAAa,MAAM,KAAK,CAAC;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QACE,QACA;YACE,SAAS;YACT,QAAQ,SAAS,cAAc;QACjC,GACA;YACE,UAAU,WAAW,WAAW;YAChC,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;QACjB;IAEJ,GAAG;QAAC,MAAM,OAAO;QAAE;QAAS;QAAU;KAAO;IAE7C,MAAM,cAAc;QAClB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAAC,KAAK;sBACd,WAAW,GAAG,CAAC,CAAC,MAAM;gBACrB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,WAAU;oBACV,OAAO;wBACL,QAAQ,SAAS,eAAe;oBAClC;;wBAEC;wBAAM;;mBANF,OAAO;;;;;YASlB;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/background-gradient.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React from \"react\";\nimport { motion } from \"motion/react\";\n\ninterface BackgroundGradientProps {\n  children?: React.ReactNode;\n  className?: string;\n  containerClassName?: string;\n  animate?: boolean;\n}\n\nexport const BackgroundGradient: React.FC<BackgroundGradientProps> = ({\n  children,\n  className,\n  containerClassName,\n  animate = true,\n}) => {\n  const variants = {\n    initial: {\n      backgroundPosition: \"0 50%\",\n    },\n    animate: {\n      backgroundPosition: [\"0, 50%\", \"100% 50%\", \"0 50%\"],\n    },\n  };\n\n  return (\n    <div className={cn(\"relative p-[4px] group\", containerClassName)}>\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] opacity-60 group-hover:opacity-100 blur-xl transition duration-500 will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#00ccb1,transparent),radial-gradient(circle_farthest-side_at_100%_0,#7b61ff,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#ffc414,transparent),radial-gradient(circle_farthest-side_at_0_0,#1ca0fb,#141316)]\"\n        )}\n      />\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#00ccb1,transparent),radial-gradient(circle_farthest-side_at_100%_0,#7b61ff,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#ffc414,transparent),radial-gradient(circle_farthest-side_at_0_0,#1ca0fb,#141316)]\"\n        )}\n      />\n\n      <div className={cn(\"relative z-10\", className)}>{children}</div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAaO,MAAM,qBAAwD,CAAC,EACpE,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,UAAU,IAAI,EACf;IACC,MAAM,WAAW;QACf,SAAS;YACP,oBAAoB;QACtB;QACA,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAQ;QACrD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;0BAC3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;;;;;;0BAGJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;;;;;;0BAIJ,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;0BAAa;;;;;;;;;;;;AAGvD", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { TextGenerateEffect } from \"@/components/ui/text-generate-effect\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport { \n  IconShieldCheck, \n  IconClock, \n  IconMapPin, \n  IconCertificate,\n  IconPhone,\n  IconCalendar\n} from \"@tabler/icons-react\";\n\nconst Hero: React.FC = () => {\n  const heroText = \"Professional Mobile Drug & DNA Testing Services - We Come to You\";\n  const subText = \"DOT Compliant • Certified Technicians • Accurate Results • Convenient Scheduling\";\n\n  const features = [\n    {\n      icon: <IconMapPin className=\"w-6 h-6\" />,\n      title: \"Mobile Service\",\n      description: \"We come to your location\"\n    },\n    {\n      icon: <IconShieldCheck className=\"w-6 h-6\" />,\n      title: \"DOT Certified\",\n      description: \"Fully compliant testing\"\n    },\n    {\n      icon: <IconClock className=\"w-6 h-6\" />,\n      title: \"Fast Results\",\n      description: \"Quick turnaround times\"\n    },\n    {\n      icon: <IconCertificate className=\"w-6 h-6\" />,\n      title: \"Accurate Testing\",\n      description: \"Medical-grade precision\"\n    }\n  ];\n\n  return (\n    <section id=\"home\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]\" />\n      \n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-72 h-72 bg-primary-500/10 rounded-full blur-3xl\"\n        animate={{\n          x: [0, 100, 0],\n          y: [0, -50, 0],\n        }}\n        transition={{\n          duration: 20,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-20 right-10 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl\"\n        animate={{\n          x: [0, -80, 0],\n          y: [0, 60, 0],\n        }}\n        transition={{\n          duration: 25,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n\n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Content */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n          >\n            {/* Badge */}\n            <motion.div\n              className=\"inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n            >\n              <IconShieldCheck className=\"w-4 h-4 mr-2\" />\n              Trusted by 500+ Businesses\n            </motion.div>\n\n            {/* Main Heading */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-5xl lg:text-7xl font-bold leading-tight\">\n                <span className=\"text-gradient-luxury\">Kalanis</span>\n                <br />\n                <span className=\"text-neutral-900\">Express</span>\n              </h1>\n              \n              <div className=\"text-xl lg:text-2xl text-neutral-600 max-w-2xl\">\n                <TextGenerateEffect \n                  words={heroText}\n                  className=\"text-xl lg:text-2xl text-neutral-600\"\n                />\n              </div>\n            </div>\n\n            {/* Subtitle */}\n            <motion.p\n              className=\"text-lg text-neutral-500 max-w-xl leading-relaxed\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.6 }}\n            >\n              {subText}\n            </motion.p>\n\n            {/* CTA Buttons */}\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.6 }}\n            >\n              <Button\n                size=\"lg\"\n                variant=\"primary\"\n                icon={<IconCalendar className=\"w-5 h-5\" />}\n                className=\"text-lg px-8 py-4\"\n              >\n                Schedule Testing\n              </Button>\n              \n              <Button\n                size=\"lg\"\n                variant=\"outline\"\n                icon={<IconPhone className=\"w-5 h-5\" />}\n                className=\"text-lg px-8 py-4\"\n              >\n                Call Now: (*************\n              </Button>\n            </motion.div>\n\n            {/* Trust Indicators */}\n            <motion.div\n              className=\"flex items-center space-x-6 pt-8\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.2, duration: 0.6 }}\n            >\n              <div className=\"text-sm text-neutral-500\">\n                <div className=\"font-semibold text-neutral-900\">24/7</div>\n                <div>Available</div>\n              </div>\n              <div className=\"w-px h-8 bg-neutral-300\" />\n              <div className=\"text-sm text-neutral-500\">\n                <div className=\"font-semibold text-neutral-900\">Same Day</div>\n                <div>Service</div>\n              </div>\n              <div className=\"w-px h-8 bg-neutral-300\" />\n              <div className=\"text-sm text-neutral-500\">\n                <div className=\"font-semibold text-neutral-900\">100%</div>\n                <div>Compliant</div>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Right Column - Features Grid */}\n          <motion.div\n            className=\"grid grid-cols-2 gap-6\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4, ease: \"easeOut\" }}\n          >\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}\n              >\n                <BackgroundGradient className=\"p-6 rounded-3xl\">\n                  <div className=\"bg-white/80 backdrop-blur-xl rounded-2xl p-6 h-full\">\n                    <div className=\"flex flex-col items-center text-center space-y-4\">\n                      <div className=\"p-3 rounded-2xl bg-primary-100 text-primary-600\">\n                        {feature.icon}\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-neutral-900 mb-2\">\n                          {feature.title}\n                        </h3>\n                        <p className=\"text-sm text-neutral-600\">\n                          {feature.description}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                </BackgroundGradient>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 1.5, duration: 0.6 }}\n      >\n        <motion.div\n          className=\"w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <motion.div\n            className=\"w-1 h-3 bg-neutral-400 rounded-full mt-2\"\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAgBA,MAAM,OAAiB;IACrB,MAAM,WAAW;IACjB,MAAM,UAAU;IAEhB,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,0NAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAK;qBAAE;oBACd,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAChB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBACf;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;;8CAG7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;;sDAExC,8OAAC,oOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAK9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAGrC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sJAAA,CAAA,qBAAkB;gDACjB,OAAO;gDACP,WAAU;;;;;;;;;;;;;;;;;8CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;8CAEvC;;;;;;8CAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAG,UAAU;oCAAI;;sDAEtC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,oBAAM,8OAAC,8NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAC9B,WAAU;sDACX;;;;;;sDAID,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAC3B,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,8OAAC;8DAAI;;;;;;;;;;;;sDAEP,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,8OAAC;8DAAI;;;;;;;;;;;;sDAEP,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAMX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;sCAExD,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;wCAAK,UAAU;oCAAI;8CAEtD,cAAA,8OAAC,kJAAA,CAAA,qBAAkB;wCAAC,WAAU;kDAC5B,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI;;;;;;kEAEf,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,8OAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhBzB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BA6B5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;0BAExC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAE5C,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;;;;;;;;;;;;;;;;;;;;;AAMxD;uCAEe", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/card.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  variant?: \"default\" | \"glass\" | \"luxury\" | \"medical\";\n  hover?: boolean;\n  padding?: \"none\" | \"sm\" | \"md\" | \"lg\" | \"xl\";\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  (\n    {\n      children,\n      className,\n      variant = \"default\",\n      hover = true,\n      padding = \"md\",\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = cn(\n      \"relative rounded-3xl transition-all duration-500 ease-out\",\n      \"border border-neutral-200/50\"\n    );\n\n    const variantClasses = {\n      default: cn(\n        \"bg-white/80 backdrop-blur-xl\",\n        \"shadow-luxury\",\n        hover && \"hover:shadow-luxury-lg hover:scale-[1.02] hover:-translate-y-2\"\n      ),\n      glass: cn(\n        \"bg-white/10 backdrop-blur-md border-white/20\",\n        \"shadow-luxury\",\n        hover && \"hover:shadow-luxury-lg hover:scale-[1.02]\"\n      ),\n      luxury: cn(\n        \"bg-gradient-to-br from-white via-primary-50/30 to-secondary-50/30\",\n        \"backdrop-blur-xl border-primary-200/30\",\n        \"shadow-luxury\",\n        hover && \"hover:shadow-luxury-lg hover:shadow-glow hover:scale-[1.02] hover:-translate-y-2\"\n      ),\n      medical: cn(\n        \"bg-gradient-to-br from-white via-secondary-50/50 to-primary-50/30\",\n        \"backdrop-blur-xl border-secondary-200/30\",\n        \"shadow-luxury\",\n        hover && \"hover:shadow-luxury-lg hover:shadow-glow-green hover:scale-[1.02] hover:-translate-y-2\"\n      ),\n    };\n\n    const paddingClasses = {\n      none: \"\",\n      sm: \"p-4\",\n      md: \"p-6\",\n      lg: \"p-8\",\n      xl: \"p-12\",\n    };\n\n    const cardClasses = cn(\n      baseClasses,\n      variantClasses[variant],\n      paddingClasses[padding],\n      className\n    );\n\n    return (\n      <motion.div\n        ref={ref}\n        className={cardClasses}\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        viewport={{ once: true }}\n        transition={{ duration: 0.6, ease: \"easeOut\" }}\n        {...props}\n      >\n        {/* Gradient overlay for luxury variant */}\n        {variant === \"luxury\" && (\n          <div className=\"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary-50/20 to-secondary-50/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100\" />\n        )}\n        \n        {/* Content */}\n        <div className=\"relative z-10\">{children}</div>\n      </motion.div>\n    );\n  }\n);\n\nCard.displayName = \"Card\";\n\n// Card Header Component\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ children, className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\"flex flex-col space-y-1.5 pb-6\", className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardHeader.displayName = \"CardHeader\";\n\n// Card Title Component\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n  gradient?: boolean;\n}\n\nconst CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(\n  ({ children, className, gradient = false, ...props }, ref) => {\n    return (\n      <h3\n        ref={ref}\n        className={cn(\n          \"text-2xl font-semibold leading-none tracking-tight\",\n          gradient && \"text-gradient-luxury\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </h3>\n    );\n  }\n);\n\nCardTitle.displayName = \"CardTitle\";\n\n// Card Description Component\ninterface CardDescriptionProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ children, className, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn(\"text-neutral-600 leading-relaxed\", className)}\n        {...props}\n      >\n        {children}\n      </p>\n    );\n  }\n);\n\nCardDescription.displayName = \"CardDescription\";\n\n// Card Content Component\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ children, className, ...props }, ref) => {\n    return (\n      <div ref={ref} className={cn(\"\", className)} {...props}>\n        {children}\n      </div>\n    );\n  }\n);\n\nCardContent.displayName = \"CardContent\";\n\n// Card Footer Component\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ children, className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\"flex items-center pt-6\", className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  CardFooter,\n};\n\nexport type {\n  CardProps,\n  CardHeaderProps,\n  CardTitleProps,\n  CardDescriptionProps,\n  CardContentProps,\n  CardFooterProps,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CACE,EACE,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,GAAG,OACJ,EACD;IAEA,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,6DACA;IAGF,MAAM,iBAAiB;QACrB,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,gCACA,iBACA,SAAS;QAEX,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACN,gDACA,iBACA,SAAS;QAEX,QAAQ,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACP,qEACA,0CACA,iBACA,SAAS;QAEX,SAAS,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,qEACA,4CACA,iBACA,SAAS;IAEb;IAEA,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,aACA,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,QAAQ,EACvB;IAGF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,UAAU;YAAE,MAAM;QAAK;QACvB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC5C,GAAG,KAAK;;YAGR,YAAY,0BACX,8OAAC;gBAAI,WAAU;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BAAiB;;;;;;;;;;;;AAGtC;AAGF,KAAK,WAAW,GAAG;AAQnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG;AASzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAChC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IACpD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,YAAY,wBACZ;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,UAAU,WAAW,GAAG;AAQxB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACtC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,gBAAgB,WAAW,GAAG;AAQ9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;kBACnD;;;;;;AAGP;AAGF,YAAY,WAAW,GAAG;AAQ1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/services.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { Card, CardHeader, CardTitle, CardDescription, CardContent } from \"@/components/ui/card\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  IconFlask,\n  IconDna,\n  IconTruck,\n  IconShieldCheck,\n  IconClock,\n  IconCertificate,\n  IconUsers,\n  IconBuilding,\n  IconCalendar,\n  IconArrowRight\n} from \"@tabler/icons-react\";\n\nconst Services: React.FC = () => {\n  const services = [\n    {\n      icon: <IconFlask className=\"w-8 h-8\" />,\n      title: \"DOT Drug Testing\",\n      description: \"Department of Transportation compliant drug and alcohol testing for commercial drivers and safety-sensitive employees.\",\n      features: [\n        \"5-Panel & 10-Panel Testing\",\n        \"Breath Alcohol Testing\",\n        \"Random Testing Programs\",\n        \"Return-to-Duty Testing\"\n      ],\n      color: \"primary\"\n    },\n    {\n      icon: <IconDna className=\"w-8 h-8\" />,\n      title: \"DNA Testing\",\n      description: \"Accurate paternity and relationship testing with legally defensible results for personal or court-ordered cases.\",\n      features: [\n        \"Paternity Testing\",\n        \"Sibling DNA Testing\",\n        \"Grandparent Testing\",\n        \"Legal & Personal Testing\"\n      ],\n      color: \"secondary\"\n    },\n    {\n      icon: <IconTruck className=\"w-8 h-8\" />,\n      title: \"Mobile Collection\",\n      description: \"Convenient on-site collection services at your workplace, home, or any location that works for you.\",\n      features: [\n        \"Workplace Testing\",\n        \"Home Collection\",\n        \"Same-Day Service\",\n        \"Flexible Scheduling\"\n      ],\n      color: \"accent\"\n    }\n  ];\n\n  const industries = [\n    {\n      icon: <IconBuilding className=\"w-6 h-6\" />,\n      title: \"Transportation\",\n      description: \"DOT compliance for trucking, aviation, and maritime companies\"\n    },\n    {\n      icon: <IconUsers className=\"w-6 h-6\" />,\n      title: \"Healthcare\",\n      description: \"Pre-employment and random testing for medical facilities\"\n    },\n    {\n      icon: <IconShieldCheck className=\"w-6 h-6\" />,\n      title: \"Construction\",\n      description: \"Safety-focused testing for construction and industrial sites\"\n    },\n    {\n      icon: <IconCertificate className=\"w-6 h-6\" />,\n      title: \"Legal Services\",\n      description: \"Court-ordered testing and legal documentation support\"\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"services\" className=\"py-24 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center max-w-4xl mx-auto mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <motion.div\n            className=\"inline-flex items-center px-6 py-3 rounded-full bg-primary-100 text-primary-700 text-sm font-semibold mb-8 shadow-sm\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n          >\n            <IconShieldCheck className=\"w-4 h-4 mr-2\" />\n            Professional Testing Services\n          </motion.div>\n\n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-8\">\n            <span className=\"text-gradient-luxury\">Comprehensive</span>\n            <br />\n            <span className=\"text-neutral-900\">Testing Solutions</span>\n          </h2>\n\n          <p className=\"text-xl text-neutral-700 leading-relaxed max-w-3xl mx-auto\">\n            From DOT compliance to DNA testing, we provide accurate, reliable results\n            with the convenience of mobile service. Our certified technicians ensure\n            professional standards every time.\n          </p>\n        </motion.div>\n\n        {/* Main Services Grid */}\n        <motion.div\n          className=\"grid lg:grid-cols-3 gap-8 mb-20\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n        >\n          {services.map((service, index) => (\n            <motion.div key={service.title} variants={itemVariants}>\n              <Card className=\"h-full bg-white border-2 border-neutral-100 hover:border-primary-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group\">\n                <CardHeader className=\"pb-4\">\n                  <div className=\"p-4 rounded-2xl w-fit mb-4 bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg\">\n                    {service.icon}\n                  </div>\n                  <CardTitle className=\"text-2xl font-bold text-neutral-900 mb-3\">\n                    {service.title}\n                  </CardTitle>\n                  <CardDescription className=\"text-base text-neutral-700 leading-relaxed\">\n                    {service.description}\n                  </CardDescription>\n                </CardHeader>\n\n                <CardContent className=\"pt-0\">\n                  <ul className=\"space-y-3 mb-8\">\n                    {service.features.map((feature, idx) => (\n                      <li key={idx} className=\"flex items-center text-neutral-800\">\n                        <div className=\"w-5 h-5 rounded-full bg-secondary-100 flex items-center justify-center mr-3 flex-shrink-0\">\n                          <IconShieldCheck className=\"w-3 h-3 text-secondary-600\" />\n                        </div>\n                        <span className=\"font-medium\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <Button\n                    variant=\"primary\"\n                    size=\"lg\"\n                    className=\"w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n                    icon={<IconArrowRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\" />}\n                    iconPosition=\"right\"\n                  >\n                    Learn More\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Industries We Serve */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <h3 className=\"text-4xl font-bold text-neutral-900 mb-6\">\n            Industries We Serve\n          </h3>\n          <p className=\"text-xl text-neutral-700 max-w-3xl mx-auto leading-relaxed\">\n            Trusted by businesses across multiple industries for reliable testing services\n          </p>\n        </motion.div>\n\n        <motion.div\n          className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n        >\n          {industries.map((industry, index) => (\n            <motion.div key={industry.title} variants={itemVariants}>\n              <Card className=\"text-center h-full bg-white border-2 border-neutral-100 hover:border-primary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group\">\n                <CardContent className=\"p-8\">\n                  <div className=\"p-4 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 text-white w-fit mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                    {industry.icon}\n                  </div>\n                  <h4 className=\"font-bold text-neutral-900 mb-3 text-lg\">\n                    {industry.title}\n                  </h4>\n                  <p className=\"text-neutral-700 leading-relaxed\">\n                    {industry.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <div className=\"bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl p-8 lg:p-12 text-white\">\n            <h3 className=\"text-3xl lg:text-4xl font-bold mb-4\">\n              Ready to Schedule Your Testing?\n            </h3>\n            <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n              Get started with professional, convenient testing services. \n              Same-day appointments available.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button\n                size=\"lg\"\n                variant=\"accent\"\n                icon={<IconCalendar className=\"w-5 h-5\" />}\n                className=\"bg-white text-primary-600 hover:bg-neutral-100\"\n              >\n                Schedule Now\n              </Button>\n              <Button\n                size=\"lg\"\n                variant=\"outline\"\n                icon={<IconClock className=\"w-5 h-5\" />}\n                className=\"border-white text-white hover:bg-white hover:text-primary-600\"\n              >\n                24/7 Support\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;AAoBA,MAAM,WAAqB;IACzB,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,oBAAM,8OAAC,8NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,8OAAC,oOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAI9C,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;8CACvC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;sCAGrC,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAQ5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;8BAEtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAqB,UAAU;sCACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,QAAQ,KAAK;;;;;;0DAEhB,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAIxB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,8OAAC;wDAAa,WAAU;;0EACtB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oOAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;;;;;;0EAE7B,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;uDAJxB;;;;;;;;;;0DASb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,oBAAM,8OAAC,kOAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDAChC,cAAa;0DACd;;;;;;;;;;;;;;;;;;2BAhCU,QAAQ,KAAK;;;;;;;;;;8BA0ClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAK5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;8BAEtB,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAsB,UAAU;sCACzC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACZ,SAAS,IAAI;;;;;;sDAEhB,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;;;;;;;;;;;;2BAVZ,SAAS,KAAK;;;;;;;;;;8BAmBnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE7C,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,oBAAM,8OAAC,8NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAC9B,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,oBAAM,8OAAC,wNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 1503, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/process.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  IconCalendar,\n  IconMapPin,\n  IconFlask,\n  IconFileText,\n  IconPhone,\n  IconClock,\n  IconShieldCheck,\n  IconArrowRight\n} from \"@tabler/icons-react\";\n\nconst Process: React.FC = () => {\n  const steps = [\n    {\n      step: \"01\",\n      icon: <IconCalendar className=\"w-8 h-8\" />,\n      title: \"Schedule Online\",\n      description: \"Book your appointment through our easy online system or call our 24/7 support line. Choose a time that works for you.\",\n      details: [\n        \"Online booking system\",\n        \"Flexible scheduling\",\n        \"Same-day availability\",\n        \"24/7 customer support\"\n      ],\n      color: \"primary\"\n    },\n    {\n      step: \"02\",\n      icon: <IconMapPin className=\"w-8 h-8\" />,\n      title: \"We Come to You\",\n      description: \"Our certified technician arrives at your chosen location with all necessary equipment and documentation.\",\n      details: [\n        \"Mobile service anywhere\",\n        \"Certified technicians\",\n        \"Professional equipment\",\n        \"Proper documentation\"\n      ],\n      color: \"secondary\"\n    },\n    {\n      step: \"03\",\n      icon: <IconFlask className=\"w-8 h-8\" />,\n      title: \"Professional Collection\",\n      description: \"Quick, professional sample collection following strict protocols to ensure accuracy and compliance.\",\n      details: [\n        \"Chain of custody\",\n        \"Sterile procedures\",\n        \"Quick collection\",\n        \"Privacy maintained\"\n      ],\n      color: \"accent\"\n    },\n    {\n      step: \"04\",\n      icon: <IconFileText className=\"w-8 h-8\" />,\n      title: \"Fast Results\",\n      description: \"Receive accurate results quickly through our secure portal or direct communication as preferred.\",\n      details: [\n        \"Secure result delivery\",\n        \"Fast turnaround\",\n        \"Multiple delivery options\",\n        \"Legal documentation\"\n      ],\n      color: \"primary\"\n    }\n  ];\n\n  const benefits = [\n    {\n      icon: <IconClock className=\"w-6 h-6\" />,\n      title: \"Save Time\",\n      description: \"No travel required - we come to your location\"\n    },\n    {\n      icon: <IconShieldCheck className=\"w-6 h-6\" />,\n      title: \"Guaranteed Accuracy\",\n      description: \"Medical-grade testing with certified technicians\"\n    },\n    {\n      icon: <IconPhone className=\"w-6 h-6\" />,\n      title: \"24/7 Support\",\n      description: \"Round-the-clock customer service and scheduling\"\n    }\n  ];\n\n  return (\n    <section id=\"process\" className=\"py-24 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center max-w-4xl mx-auto mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <motion.div\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-secondary-100 text-secondary-700 text-sm font-medium mb-6\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n          >\n            <IconClock className=\"w-4 h-4 mr-2\" />\n            Simple Process\n          </motion.div>\n          \n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            <span className=\"text-neutral-900\">How It</span>\n            <br />\n            <span className=\"text-gradient-luxury\">Works</span>\n          </h2>\n          \n          <p className=\"text-xl text-neutral-600 leading-relaxed\">\n            Our streamlined process makes testing convenient and stress-free. \n            From booking to results, we handle everything professionally.\n          </p>\n        </motion.div>\n\n        {/* Process Steps */}\n        <div className=\"relative mb-20\">\n          {/* Connection Line */}\n          <div className=\"hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-200 via-secondary-200 to-accent-200 transform -translate-y-1/2 z-0\" />\n          \n          <div className=\"grid lg:grid-cols-4 gap-8 relative z-10\">\n            {steps.map((step, index) => (\n              <motion.div\n                key={step.step}\n                className=\"relative\"\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ \n                  duration: 0.8, \n                  delay: index * 0.2,\n                  ease: \"easeOut\" \n                }}\n              >\n                <Card variant=\"luxury\" className=\"text-center h-full group\">\n                  <CardContent className=\"p-8\">\n                    {/* Step Number */}\n                    <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${\n                        step.color === 'primary' ? 'bg-primary-500' :\n                        step.color === 'secondary' ? 'bg-secondary-500' :\n                        'bg-accent-500'\n                      }`}>\n                        {step.step}\n                      </div>\n                    </div>\n\n                    {/* Icon */}\n                    <div className={`p-4 rounded-2xl w-fit mx-auto mb-6 mt-4 ${\n                      step.color === 'primary' ? 'bg-primary-100 text-primary-600' :\n                      step.color === 'secondary' ? 'bg-secondary-100 text-secondary-600' :\n                      'bg-accent-100 text-accent-600'\n                    }`}>\n                      {step.icon}\n                    </div>\n\n                    {/* Content */}\n                    <h3 className=\"text-xl font-bold text-neutral-900 mb-4\">\n                      {step.title}\n                    </h3>\n                    \n                    <p className=\"text-neutral-600 mb-6 leading-relaxed\">\n                      {step.description}\n                    </p>\n\n                    {/* Details */}\n                    <ul className=\"space-y-2 text-sm text-neutral-500\">\n                      {step.details.map((detail, idx) => (\n                        <li key={idx} className=\"flex items-center justify-center\">\n                          <IconShieldCheck className=\"w-3 h-3 text-secondary-500 mr-2 flex-shrink-0\" />\n                          {detail}\n                        </li>\n                      ))}\n                    </ul>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Benefits Section */}\n        <motion.div\n          className=\"grid md:grid-cols-3 gap-8 mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          {benefits.map((benefit, index) => (\n            <motion.div\n              key={benefit.title}\n              className=\"text-center\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ \n                duration: 0.6, \n                delay: index * 0.1,\n                ease: \"easeOut\" \n              }}\n            >\n              <div className=\"p-4 rounded-2xl bg-primary-100 text-primary-600 w-fit mx-auto mb-4\">\n                {benefit.icon}\n              </div>\n              <h4 className=\"text-lg font-semibold text-neutral-900 mb-2\">\n                {benefit.title}\n              </h4>\n              <p className=\"text-neutral-600\">\n                {benefit.description}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          className=\"text-center bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-8 lg:p-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <h3 className=\"text-3xl lg:text-4xl font-bold text-neutral-900 mb-4\">\n            Ready to Get Started?\n          </h3>\n          <p className=\"text-xl text-neutral-600 mb-8 max-w-2xl mx-auto\">\n            Schedule your testing appointment today and experience the convenience \n            of professional mobile testing services.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              size=\"lg\"\n              variant=\"primary\"\n              icon={<IconCalendar className=\"w-5 h-5\" />}\n            >\n              Schedule Testing\n            </Button>\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              icon={<IconArrowRight className=\"w-5 h-5\" />}\n              iconPosition=\"right\"\n            >\n              View Pricing\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Process;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAiBA,MAAM,UAAoB;IACxB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,oBAAM,8OAAC,8NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,0NAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,8NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;YACP,aAAa;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,OAAO;QACT;KACD;IAED,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,8OAAC,wNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAO1D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCACV,UAAU;wCACV,OAAO,QAAQ;wCACf,MAAM;oCACR;8CAEA,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAS,WAAU;kDAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DAErB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAW,CAAC,mFAAmF,EAClG,KAAK,KAAK,KAAK,YAAY,mBAC3B,KAAK,KAAK,KAAK,cAAc,qBAC7B,iBACA;kEACC,KAAK,IAAI;;;;;;;;;;;8DAKd,8OAAC;oDAAI,WAAW,CAAC,wCAAwC,EACvD,KAAK,KAAK,KAAK,YAAY,oCAC3B,KAAK,KAAK,KAAK,cAAc,wCAC7B,iCACA;8DACC,KAAK,IAAI;;;;;;8DAIZ,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAGb,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAInB,8OAAC;oDAAG,WAAU;8DACX,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,oBACzB,8OAAC;4DAAa,WAAU;;8EACtB,8OAAC,oOAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;gEAC1B;;2DAFM;;;;;;;;;;;;;;;;;;;;;mCA7CZ,KAAK,IAAI;;;;;;;;;;;;;;;;8BA2DtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE5C,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,MAAM;4BACR;;8CAEA,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAEf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAlBjB,QAAQ,KAAK;;;;;;;;;;8BAyBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,oBAAM,8OAAC,8NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;8CAC/B;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,oBAAM,8OAAC,kOAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;oCAChC,cAAa;8CACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/ui/animated-tooltip.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport {\n  motion,\n  useTransform,\n  AnimatePresence,\n  useMotionValue,\n  useSpring,\n} from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface TooltipItem {\n  id: number;\n  name: string;\n  designation: string;\n  image: string;\n}\n\ninterface AnimatedTooltipProps {\n  items: TooltipItem[];\n  className?: string;\n}\n\nexport const AnimatedTooltip: React.FC<AnimatedTooltipProps> = ({\n  items,\n  className,\n}) => {\n  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n  const springConfig = { stiffness: 100, damping: 5 };\n  const x = useMotionValue(0);\n\n  // Rotate the tooltip\n  const rotate = useSpring(\n    useTransform(x, [-100, 100], [-45, 45]),\n    springConfig\n  );\n\n  // Translate the tooltip\n  const translateX = useSpring(\n    useTransform(x, [-100, 100], [-50, 50]),\n    springConfig\n  );\n\n  const handleMouseMove = (event: React.MouseEvent<HTMLImageElement>) => {\n    const halfWidth = event.currentTarget.offsetWidth / 2;\n    x.set(event.nativeEvent.offsetX - halfWidth);\n  };\n\n  return (\n    <div className={cn(\"flex flex-row items-center justify-center\", className)}>\n      {items.map((item, idx) => (\n        <div\n          className=\"group relative -mr-4\"\n          key={item.name}\n          onMouseEnter={() => setHoveredIndex(item.id)}\n          onMouseLeave={() => setHoveredIndex(null)}\n        >\n          <AnimatePresence mode=\"popLayout\">\n            {hoveredIndex === item.id && (\n              <motion.div\n                initial={{ opacity: 0, y: 20, scale: 0.6 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  scale: 1,\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 260,\n                    damping: 10,\n                  },\n                }}\n                exit={{ opacity: 0, y: 20, scale: 0.6 }}\n                style={{\n                  translateX: translateX,\n                  rotate: rotate,\n                  whiteSpace: \"nowrap\",\n                }}\n                className=\"absolute -top-16 left-1/2 z-50 flex -translate-x-1/2 flex-col items-center justify-center rounded-md bg-black px-4 py-2 text-xs shadow-xl\"\n              >\n                <div className=\"absolute inset-x-10 -bottom-px z-30 h-px w-[20%] bg-gradient-to-r from-transparent via-emerald-500 to-transparent\" />\n                <div className=\"absolute -bottom-px left-10 z-30 h-px w-[40%] bg-gradient-to-r from-transparent via-sky-500 to-transparent\" />\n                <div className=\"relative z-30 text-base font-bold text-white\">\n                  {item.name}\n                </div>\n                <div className=\"text-xs text-white\">{item.designation}</div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n          <img\n            onMouseMove={handleMouseMove}\n            height={100}\n            width={100}\n            src={item.image}\n            alt={item.name}\n            className=\"relative !m-0 h-14 w-14 rounded-full border-2 border-white object-cover object-top !p-0 transition duration-500 group-hover:z-30 group-hover:scale-105\"\n          />\n        </div>\n      ))}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAVA;;;;;AAwBO,MAAM,kBAAkD,CAAC,EAC9D,KAAK,EACL,SAAS,EACV;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,eAAe;QAAE,WAAW;QAAK,SAAS;IAAE;IAClD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAEzB,qBAAqB;IACrB,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EACrB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GACtC;IAGF,wBAAwB;IACxB,MAAM,aAAa,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EACzB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GACtC;IAGF,MAAM,kBAAkB,CAAC;QACvB,MAAM,YAAY,MAAM,aAAa,CAAC,WAAW,GAAG;QACpD,EAAE,GAAG,CAAC,MAAM,WAAW,CAAC,OAAO,GAAG;IACpC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;kBAC7D,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;gBACC,WAAU;gBAEV,cAAc,IAAM,gBAAgB,KAAK,EAAE;gBAC3C,cAAc,IAAM,gBAAgB;;kCAEpC,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,iBAAiB,KAAK,EAAE,kBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACzC,SAAS;gCACP,SAAS;gCACT,GAAG;gCACH,OAAO;gCACP,YAAY;oCACV,MAAM;oCACN,WAAW;oCACX,SAAS;gCACX;4BACF;4BACA,MAAM;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACtC,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,YAAY;4BACd;4BACA,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;8CAAsB,KAAK,WAAW;;;;;;;;;;;;;;;;;kCAI3D,8OAAC;wBACC,aAAa;wBACb,QAAQ;wBACR,OAAO;wBACP,KAAK,KAAK,KAAK;wBACf,KAAK,KAAK,IAAI;wBACd,WAAU;;;;;;;eAzCP,KAAK,IAAI;;;;;;;;;;AA+CxB", "debugId": null}}, {"offset": {"line": 2187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/testimonials.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { AnimatedTooltip } from \"@/components/ui/animated-tooltip\";\nimport {\n  IconStar,\n  IconQuote,\n  IconShieldCheck,\n  IconCertificate,\n  Icon<PERSON><PERSON>ck,\n  IconClock\n} from \"@tabler/icons-react\";\n\nconst Testimonials: React.FC = () => {\n  const testimonials = [\n    {\n      name: \"<PERSON>\",\n      role: \"HR Director\",\n      company: \"TransLogistics Inc.\",\n      content: \"Kalanis Express has been a game-changer for our DOT compliance. Their mobile service saves us hours of employee downtime, and their technicians are always professional and thorough.\",\n      rating: 5,\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\"\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Safety Manager\",\n      company: \"BuildRight Construction\",\n      content: \"The convenience of on-site testing is incredible. We can maintain our safety standards without disrupting our work schedule. Highly recommend their services.\",\n      rating: 5,\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Legal Assistant\",\n      company: \"<PERSON> & Associates\",\n      content: \"For court-ordered DNA testing, Kalanis Express provides the professionalism and accuracy we need. Their documentation is always perfect for legal proceedings.\",\n      rating: 5,\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\"\n    },\n    {\n      name: \"David Thompson\",\n      role: \"Fleet Manager\",\n      company: \"Metro Delivery Services\",\n      content: \"Same-day service and accurate results every time. Their mobile testing has streamlined our hiring process and keeps our drivers compliant with DOT regulations.\",\n      rating: 5,\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\"\n    }\n  ];\n\n  const stats = [\n    {\n      number: \"500+\",\n      label: \"Businesses Served\",\n      icon: <IconShieldCheck className=\"w-6 h-6\" />\n    },\n    {\n      number: \"10,000+\",\n      label: \"Tests Completed\",\n      icon: <IconCertificate className=\"w-6 h-6\" />\n    },\n    {\n      number: \"99.9%\",\n      label: \"Accuracy Rate\",\n      icon: <IconStar className=\"w-6 h-6\" />\n    },\n    {\n      number: \"24/7\",\n      label: \"Support Available\",\n      icon: <IconClock className=\"w-6 h-6\" />\n    }\n  ];\n\n  const certifications = [\n    {\n      name: \"DOT Certified\",\n      description: \"Department of Transportation approved testing procedures\"\n    },\n    {\n      name: \"SAMHSA Guidelines\",\n      description: \"Following Substance Abuse and Mental Health Services guidelines\"\n    },\n    {\n      name: \"Chain of Custody\",\n      description: \"Strict protocols for sample handling and documentation\"\n    },\n    {\n      name: \"Legal Compliance\",\n      description: \"Court-admissible results and proper documentation\"\n    }\n  ];\n\n  const teamMembers = [\n    {\n      id: 1,\n      name: \"Dr. Sarah Wilson\",\n      designation: \"Medical Director\",\n      image: \"https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face\"\n    },\n    {\n      id: 2,\n      name: \"Mike Rodriguez\",\n      designation: \"Lead Technician\",\n      image: \"https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face\"\n    },\n    {\n      id: 3,\n      name: \"Lisa Chen\",\n      designation: \"Quality Assurance\",\n      image: \"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face\"\n    },\n    {\n      id: 4,\n      name: \"James Parker\",\n      designation: \"Mobile Technician\",\n      image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n    }\n  ];\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-neutral-50 via-white to-primary-50/30\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center max-w-4xl mx-auto mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <motion.div\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-accent-100 text-accent-700 text-sm font-medium mb-6\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n          >\n            <IconStar className=\"w-4 h-4 mr-2\" />\n            Trusted by Professionals\n          </motion.div>\n          \n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            <span className=\"text-neutral-900\">What Our</span>\n            <br />\n            <span className=\"text-gradient-luxury\">Clients Say</span>\n          </h2>\n          \n          <p className=\"text-xl text-neutral-600 leading-relaxed\">\n            Don't just take our word for it. See what businesses and individuals \n            say about our professional testing services.\n          </p>\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          className=\"grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              className=\"text-center\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ \n                duration: 0.6, \n                delay: index * 0.1,\n                ease: \"easeOut\" \n              }}\n            >\n              <div className=\"p-4 rounded-2xl bg-primary-100 text-primary-600 w-fit mx-auto mb-4\">\n                {stat.icon}\n              </div>\n              <div className=\"text-3xl lg:text-4xl font-bold text-neutral-900 mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-neutral-600 font-medium\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Testimonials Grid */}\n        <motion.div\n          className=\"grid lg:grid-cols-2 gap-8 mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          {testimonials.map((testimonial, index) => (\n            <motion.div\n              key={testimonial.name}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ \n                duration: 0.6, \n                delay: index * 0.1,\n                ease: \"easeOut\" \n              }}\n            >\n              <Card variant=\"luxury\" className=\"h-full\">\n                <CardContent className=\"p-8\">\n                  {/* Quote Icon */}\n                  <IconQuote className=\"w-8 h-8 text-primary-500 mb-4\" />\n                  \n                  {/* Rating */}\n                  <div className=\"flex items-center mb-4\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <IconStar key={i} className=\"w-5 h-5 text-accent-500 fill-current\" />\n                    ))}\n                  </div>\n                  \n                  {/* Content */}\n                  <p className=\"text-neutral-700 leading-relaxed mb-6 text-lg\">\n                    \"{testimonial.content}\"\n                  </p>\n                  \n                  {/* Author */}\n                  <div className=\"flex items-center\">\n                    <img\n                      src={testimonial.avatar}\n                      alt={testimonial.name}\n                      className=\"w-12 h-12 rounded-full object-cover mr-4\"\n                    />\n                    <div>\n                      <div className=\"font-semibold text-neutral-900\">\n                        {testimonial.name}\n                      </div>\n                      <div className=\"text-sm text-neutral-600\">\n                        {testimonial.role}, {testimonial.company}\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Team Section */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <h3 className=\"text-3xl font-bold text-neutral-900 mb-4\">\n            Meet Our Certified Team\n          </h3>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto mb-8\">\n            Our experienced professionals ensure accurate results and exceptional service\n          </p>\n          \n          <div className=\"flex justify-center\">\n            <AnimatedTooltip items={teamMembers} />\n          </div>\n        </motion.div>\n\n        {/* Certifications */}\n        <motion.div\n          className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          {certifications.map((cert, index) => (\n            <motion.div\n              key={cert.name}\n              className=\"text-center p-6 rounded-2xl bg-white border border-neutral-200 hover:border-primary-300 transition-colors duration-300\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ \n                duration: 0.6, \n                delay: index * 0.1,\n                ease: \"easeOut\" \n              }}\n            >\n              <IconShieldCheck className=\"w-8 h-8 text-secondary-500 mx-auto mb-3\" />\n              <h4 className=\"font-semibold text-neutral-900 mb-2\">\n                {cert.name}\n              </h4>\n              <p className=\"text-sm text-neutral-600\">\n                {cert.description}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAeA,MAAM,eAAyB;IAC7B,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;KACD;IAED,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;QACnC;QACA;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;QACnC;QACA;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,QAAQ;YACR,OAAO;YACP,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,8OAAC,sNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAO1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE5C,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,MAAM;4BACR;;8CAEA,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BAlBR,KAAK,KAAK;;;;;;;;;;8BAyBrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE5C,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,MAAM;4BACR;sCAEA,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAS,WAAU;0CAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC,wNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDAGrB,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM,YAAY,MAAM;6CAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,sNAAA,CAAA,WAAQ;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAKnB,8OAAC;4CAAE,WAAU;;gDAAgD;gDACzD,YAAY,OAAO;gDAAC;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAK,YAAY,MAAM;oDACvB,KAAK,YAAY,IAAI;oDACrB,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACZ,YAAY,IAAI;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;gEACZ,YAAY,IAAI;gEAAC;gEAAG,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAvC7C,YAAY,IAAI;;;;;;;;;;8BAkD3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,+IAAA,CAAA,kBAAe;gCAAC,OAAO;;;;;;;;;;;;;;;;;8BAK5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE5C,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,MAAM;4BACR;;8CAEA,8OAAC,oOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAG,WAAU;8CACX,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BAhBd,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAwB5B;uCAEe", "debugId": null}}, {"offset": {"line": 2785, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/about.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport {\n  IconShieldCheck,\n  IconCertificate,\n  IconUsers,\n  IconTruck,\n  IconClock,\n  IconHeart,\n  IconTarget,\n  IconAward,\n  IconPhone\n} from \"@tabler/icons-react\";\n\nconst About: React.FC = () => {\n  const values = [\n    {\n      icon: <IconShieldCheck className=\"w-8 h-8\" />,\n      title: \"Accuracy\",\n      description: \"Medical-grade precision in every test we perform, ensuring reliable results you can trust.\"\n    },\n    {\n      icon: <IconHeart className=\"w-8 h-8\" />,\n      title: \"Compassion\",\n      description: \"Understanding that testing can be stressful, we provide caring, professional service.\"\n    },\n    {\n      icon: <IconClock className=\"w-8 h-8\" />,\n      title: \"Convenience\",\n      description: \"Mobile service that comes to you, saving time and reducing stress for our clients.\"\n    },\n    {\n      icon: <IconCertificate className=\"w-8 h-8\" />,\n      title: \"Compliance\",\n      description: \"Strict adherence to DOT regulations and legal requirements for defensible results.\"\n    }\n  ];\n\n  const achievements = [\n    {\n      icon: <IconAward className=\"w-6 h-6\" />,\n      title: \"Industry Recognition\",\n      description: \"Certified by leading health organizations\"\n    },\n    {\n      icon: <IconUsers className=\"w-6 h-6\" />,\n      title: \"Experienced Team\",\n      description: \"Over 50 years combined experience\"\n    },\n    {\n      icon: <IconTarget className=\"w-6 h-6\" />,\n      title: \"Proven Results\",\n      description: \"99.9% accuracy rate across all testing\"\n    }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-24 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center max-w-4xl mx-auto mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <motion.div\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-secondary-100 text-secondary-700 text-sm font-medium mb-6\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n          >\n            <IconHeart className=\"w-4 h-4 mr-2\" />\n            About Kalanis Express\n          </motion.div>\n          \n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            <span className=\"text-gradient-luxury\">Professional</span>\n            <br />\n            <span className=\"text-neutral-900\">Testing Excellence</span>\n          </h2>\n          \n          <p className=\"text-xl text-neutral-600 leading-relaxed\">\n            Founded on the principles of accuracy, convenience, and compassion, \n            Kalanis Express brings professional testing services directly to you.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center mb-20\">\n          {/* Left Column - Story */}\n          <motion.div\n            className=\"space-y-6\"\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n          >\n            <h3 className=\"text-3xl font-bold text-neutral-900\">\n              Our Story\n            </h3>\n            \n            <div className=\"space-y-4 text-lg text-neutral-600 leading-relaxed\">\n              <p>\n                Kalanis Express was born from a simple observation: traditional testing \n                services were inconvenient, time-consuming, and often stressful for both \n                businesses and individuals.\n              </p>\n              \n              <p>\n                Our founder, Sylonda, recognized the need for a more convenient approach. \n                With years of experience in healthcare and a passion for helping others, \n                she created a mobile testing service that brings professional, accurate \n                testing directly to your location.\n              </p>\n              \n              <p>\n                Today, we serve hundreds of businesses and individuals across the region, \n                providing DOT-compliant drug testing, accurate DNA testing, and exceptional \n                customer service that puts your needs first.\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 pt-4\">\n              <Button\n                size=\"lg\"\n                variant=\"primary\"\n                icon={<IconPhone className=\"w-5 h-5\" />}\n              >\n                Contact Us\n              </Button>\n              <Button\n                size=\"lg\"\n                variant=\"outline\"\n              >\n                Learn More\n              </Button>\n            </div>\n          </motion.div>\n\n          {/* Right Column - Achievements */}\n          <motion.div\n            className=\"space-y-6\"\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.2, ease: \"easeOut\" }}\n          >\n            <BackgroundGradient>\n              <Card variant=\"luxury\" className=\"p-8\">\n                <CardContent className=\"space-y-8\">\n                  <h3 className=\"text-2xl font-bold text-neutral-900 text-center\">\n                    Why Choose Us\n                  </h3>\n                  \n                  {achievements.map((achievement, index) => (\n                    <motion.div\n                      key={achievement.title}\n                      className=\"flex items-start space-x-4\"\n                      initial={{ opacity: 0, y: 20 }}\n                      whileInView={{ opacity: 1, y: 0 }}\n                      viewport={{ once: true }}\n                      transition={{ \n                        duration: 0.6, \n                        delay: 0.4 + index * 0.1,\n                        ease: \"easeOut\" \n                      }}\n                    >\n                      <div className=\"p-3 rounded-2xl bg-primary-100 text-primary-600 flex-shrink-0\">\n                        {achievement.icon}\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-neutral-900 mb-1\">\n                          {achievement.title}\n                        </h4>\n                        <p className=\"text-neutral-600\">\n                          {achievement.description}\n                        </p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </CardContent>\n              </Card>\n            </BackgroundGradient>\n          </motion.div>\n        </div>\n\n        {/* Values Section */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <h3 className=\"text-3xl font-bold text-neutral-900 mb-4\">\n            Our Core Values\n          </h3>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            These principles guide everything we do and ensure exceptional service for every client\n          </p>\n        </motion.div>\n\n        <motion.div\n          className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          {values.map((value, index) => (\n            <motion.div\n              key={value.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ \n                duration: 0.6, \n                delay: index * 0.1,\n                ease: \"easeOut\" \n              }}\n            >\n              <Card hover className=\"text-center h-full\">\n                <CardContent className=\"p-8\">\n                  <div className=\"p-4 rounded-2xl bg-secondary-100 text-secondary-600 w-fit mx-auto mb-6\">\n                    {value.icon}\n                  </div>\n                  <h4 className=\"text-xl font-semibold text-neutral-900 mb-4\">\n                    {value.title}\n                  </h4>\n                  <p className=\"text-neutral-600 leading-relaxed\">\n                    {value.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAmBA,MAAM,QAAkB;IACtB,MAAM,SAAS;QACb;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,eAAe;QACnB;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,0NAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,8OAAC,wNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;8CACvC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;sCAGrC,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAM1D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;;8CAE7C,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDAMH,8OAAC;sDAAE;;;;;;sDAOH,8OAAC;sDAAE;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;sDAC5B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;sDACT;;;;;;;;;;;;;;;;;;sCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;sCAEzD,cAAA,8OAAC,kJAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,SAAQ;oCAAS,WAAU;8CAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;4CAI/D,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,UAAU;wDAAE,MAAM;oDAAK;oDACvB,YAAY;wDACV,UAAU;wDACV,OAAO,MAAM,QAAQ;wDACrB,MAAM;oDACR;;sEAEA,8OAAC;4DAAI,WAAU;sEACZ,YAAY,IAAI;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,YAAY,KAAK;;;;;;8EAEpB,8OAAC;oEAAE,WAAU;8EACV,YAAY,WAAW;;;;;;;;;;;;;mDAnBvB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BA+BpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE5C,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,MAAM;4BACR;sCAEA,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,KAAK;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI;;;;;;sDAEb,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;;;;;;;;;;;;2BAnBnB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;AA6B9B;uCAEe", "debugId": null}}, {"offset": {"line": 3350, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/contact.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport {\n  IconPhone,\n  IconMail,\n  IconMapPin,\n  IconClock,\n  IconCalendar,\n  IconSend,\n  IconShieldCheck,\n  IconCertificate\n} from \"@tabler/icons-react\";\n\nconst Contact: React.FC = () => {\n  const contactInfo = [\n    {\n      icon: <IconPhone className=\"w-6 h-6\" />,\n      title: \"Call Us\",\n      info: \"(*************\",\n      description: \"24/7 Support Available\",\n      action: \"tel:+15551234567\"\n    },\n    {\n      icon: <IconMail className=\"w-6 h-6\" />,\n      title: \"Email Us\",\n      info: \"<EMAIL>\",\n      description: \"Quick Response Guaranteed\",\n      action: \"mailto:<EMAIL>\"\n    },\n    {\n      icon: <IconMapPin className=\"w-6 h-6\" />,\n      title: \"Service Area\",\n      info: \"Metro Area & Surrounding\",\n      description: \"Mobile Service Available\",\n      action: null\n    },\n    {\n      icon: <IconClock className=\"w-6 h-6\" />,\n      title: \"Hours\",\n      info: \"24/7 Emergency Service\",\n      description: \"Flexible Scheduling\",\n      action: null\n    }\n  ];\n\n  const services = [\n    \"DOT Drug Testing\",\n    \"DNA Testing\",\n    \"Mobile Collection\",\n    \"Same-Day Service\",\n    \"Court-Ordered Testing\",\n    \"Workplace Testing\"\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-24 bg-gradient-to-br from-primary-50 via-white to-secondary-50\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center max-w-4xl mx-auto mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <motion.div\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-6\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n          >\n            <IconPhone className=\"w-4 h-4 mr-2\" />\n            Get In Touch\n          </motion.div>\n          \n          <h2 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n            <span className=\"text-neutral-900\">Ready to</span>\n            <br />\n            <span className=\"text-gradient-luxury\">Get Started?</span>\n          </h2>\n          \n          <p className=\"text-xl text-neutral-600 leading-relaxed\">\n            Contact us today to schedule your testing appointment or learn more \n            about our professional mobile testing services.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16 mb-16\">\n          {/* Left Column - Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n          >\n            <BackgroundGradient>\n              <Card variant=\"luxury\" className=\"p-8\">\n                <CardContent>\n                  <h3 className=\"text-2xl font-bold text-neutral-900 mb-6\">\n                    Schedule Your Testing\n                  </h3>\n                  \n                  <form className=\"space-y-6\">\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                          First Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          className=\"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200\"\n                          placeholder=\"John\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                          Last Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          className=\"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200\"\n                          placeholder=\"Doe\"\n                        />\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                        Email Address\n                      </label>\n                      <input\n                        type=\"email\"\n                        className=\"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                        Phone Number\n                      </label>\n                      <input\n                        type=\"tel\"\n                        className=\"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200\"\n                        placeholder=\"(*************\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                        Service Needed\n                      </label>\n                      <select className=\"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200\">\n                        <option>Select a service</option>\n                        <option>DOT Drug Testing</option>\n                        <option>DNA Testing</option>\n                        <option>Mobile Collection</option>\n                        <option>Other</option>\n                      </select>\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                        Message\n                      </label>\n                      <textarea\n                        rows={4}\n                        className=\"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200\"\n                        placeholder=\"Tell us about your testing needs...\"\n                      />\n                    </div>\n                    \n                    <Button\n                      size=\"lg\"\n                      variant=\"primary\"\n                      className=\"w-full\"\n                      icon={<IconSend className=\"w-5 h-5\" />}\n                    >\n                      Send Message\n                    </Button>\n                  </form>\n                </CardContent>\n              </Card>\n            </BackgroundGradient>\n          </motion.div>\n\n          {/* Right Column - Contact Info */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.2, ease: \"easeOut\" }}\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold text-neutral-900 mb-6\">\n                Contact Information\n              </h3>\n              \n              <div className=\"space-y-4\">\n                {contactInfo.map((contact, index) => (\n                  <motion.div\n                    key={contact.title}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    viewport={{ once: true }}\n                    transition={{ \n                      duration: 0.6, \n                      delay: 0.4 + index * 0.1,\n                      ease: \"easeOut\" \n                    }}\n                  >\n                    <Card hover className=\"p-6\">\n                      <CardContent className=\"flex items-center space-x-4\">\n                        <div className=\"p-3 rounded-2xl bg-primary-100 text-primary-600 flex-shrink-0\">\n                          {contact.icon}\n                        </div>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-semibold text-neutral-900\">\n                            {contact.title}\n                          </h4>\n                          <p className=\"text-lg text-primary-600 font-medium\">\n                            {contact.info}\n                          </p>\n                          <p className=\"text-sm text-neutral-600\">\n                            {contact.description}\n                          </p>\n                        </div>\n                        {contact.action && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => window.open(contact.action!, '_self')}\n                          >\n                            Contact\n                          </Button>\n                        )}\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div>\n              <h4 className=\"text-xl font-bold text-neutral-900 mb-4\">\n                Quick Actions\n              </h4>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Button\n                  size=\"lg\"\n                  variant=\"primary\"\n                  icon={<IconCalendar className=\"w-5 h-5\" />}\n                  className=\"flex-1\"\n                >\n                  Schedule Now\n                </Button>\n                <Button\n                  size=\"lg\"\n                  variant=\"secondary\"\n                  icon={<IconPhone className=\"w-5 h-5\" />}\n                  className=\"flex-1\"\n                >\n                  Call Now\n                </Button>\n              </div>\n            </div>\n\n            {/* Services List */}\n            <div>\n              <h4 className=\"text-xl font-bold text-neutral-900 mb-4\">\n                Our Services\n              </h4>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {services.map((service, index) => (\n                  <div\n                    key={service}\n                    className=\"flex items-center text-neutral-600\"\n                  >\n                    <IconShieldCheck className=\"w-4 h-4 text-secondary-500 mr-2 flex-shrink-0\" />\n                    <span className=\"text-sm\">{service}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Emergency Contact CTA */}\n        <motion.div\n          className=\"text-center bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl p-8 lg:p-12 text-white\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        >\n          <IconCertificate className=\"w-16 h-16 mx-auto mb-6 opacity-80\" />\n          <h3 className=\"text-3xl lg:text-4xl font-bold mb-4\">\n            Need Emergency Testing?\n          </h3>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            We provide 24/7 emergency testing services for urgent situations. \n            Call now for immediate assistance.\n          </p>\n          <Button\n            size=\"lg\"\n            variant=\"accent\"\n            icon={<IconPhone className=\"w-5 h-5\" />}\n            className=\"bg-white text-primary-600 hover:bg-neutral-100\"\n          >\n            Emergency Line: (*************\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAkBA,MAAM,UAAoB;IACxB,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;QACV;QACA;YACE,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;QACV;QACA;YACE,oBAAM,8OAAC,0NAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;QACV;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;QACV;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,8OAAC,wNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAM1D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;sCAE7C,cAAA,8OAAC,kJAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,SAAQ;oCAAS,WAAU;8CAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAIzD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkD;;;;;;kFAGnE,8OAAC;wEACC,MAAK;wEACL,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAGhB,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkD;;;;;;kFAGnE,8OAAC;wEACC,MAAK;wEACL,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAKlB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAkD;;;;;;0EAGnE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAkD;;;;;;0EAGnE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAkD;;;;;;0EAGnE,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;;;;;;;;;;;;;kEAIZ,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAkD;;;;;;0EAGnE,8OAAC;gEACC,MAAM;gEACN,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACV,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;kEAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;;8CAEzD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAIzD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,UAAU;wDAAE,MAAM;oDAAK;oDACvB,YAAY;wDACV,UAAU;wDACV,OAAO,MAAM,QAAQ;wDACrB,MAAM;oDACR;8DAEA,cAAA,8OAAC,gIAAA,CAAA,OAAI;wDAAC,KAAK;wDAAC,WAAU;kEACpB,cAAA,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI;;;;;;8EAEf,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,QAAQ,KAAK;;;;;;sFAEhB,8OAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI;;;;;;sFAEf,8OAAC;4EAAE,WAAU;sFACV,QAAQ,WAAW;;;;;;;;;;;;gEAGvB,QAAQ,MAAM,kBACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,MAAM,EAAG;8EAC7C;;;;;;;;;;;;;;;;;mDA/BF,QAAQ,KAAK;;;;;;;;;;;;;;;;8CA2C1B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0C;;;;;;sDAGxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,oBAAM,8OAAC,8NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAC9B,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,oBAAM,8OAAC,wNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOL,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0C;;;;;;sDAGxD,8OAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC,oOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;sEAC3B,8OAAC;4DAAK,WAAU;sEAAW;;;;;;;mDAJtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAajB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAE7C,8OAAC,oOAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;sCAC3B,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAIzD,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,oBAAM,8OAAC,wNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 4143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Kalanis%20Express/kalanis-express/src/components/sections/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"motion/react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  IconPhone,\n  IconMail,\n  IconMapPin,\n  IconClock,\n  IconShieldCheck,\n  IconCertificate,\n  IconBrandFacebook,\n  IconBrandTwitter,\n  IconBrandLinkedin,\n  IconBrandInstagram,\n  IconArrowUp\n} from \"@tabler/icons-react\";\n\nconst Footer: React.FC = () => {\n  const quickLinks = [\n    { name: \"Home\", href: \"#home\" },\n    { name: \"Services\", href: \"#services\" },\n    { name: \"Process\", href: \"#process\" },\n    { name: \"About\", href: \"#about\" },\n    { name: \"Contact\", href: \"#contact\" }\n  ];\n\n  const services = [\n    { name: \"DOT Drug Testing\", href: \"#dot-testing\" },\n    { name: \"DNA Testing\", href: \"#dna-testing\" },\n    { name: \"Mobile Collection\", href: \"#mobile-service\" },\n    { name: \"Emergency Testing\", href: \"#emergency\" },\n    { name: \"Workplace Testing\", href: \"#workplace\" }\n  ];\n\n  const legalLinks = [\n    { name: \"Privacy Policy\", href: \"/privacy\" },\n    { name: \"Terms of Service\", href: \"/terms\" },\n    { name: \"HIPAA Compliance\", href: \"/hipaa\" },\n    { name: \"DOT Regulations\", href: \"/dot-compliance\" }\n  ];\n\n  const socialLinks = [\n    { icon: <IconBrandFacebook className=\"w-5 h-5\" />, href: \"#\", name: \"Facebook\" },\n    { icon: <IconBrandTwitter className=\"w-5 h-5\" />, href: \"#\", name: \"Twitter\" },\n    { icon: <IconBrandLinkedin className=\"w-5 h-5\" />, href: \"#\", name: \"LinkedIn\" },\n    { icon: <IconBrandInstagram className=\"w-5 h-5\" />, href: \"#\", name: \"Instagram\" }\n  ];\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <footer className=\"bg-neutral-900 text-white\">\n      {/* Main Footer Content */}\n      <div className=\"container mx-auto px-6 py-16\">\n        <div className=\"grid lg:grid-cols-4 gap-12\">\n          {/* Company Info */}\n          <motion.div\n            className=\"lg:col-span-1\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n          >\n            <div className=\"mb-6\">\n              <h3 className=\"text-3xl font-bold\">\n                <span className=\"text-gradient-luxury\">Kalanis</span>\n                <br />\n                <span className=\"text-white\">Express</span>\n              </h3>\n              <p className=\"text-neutral-400 mt-4 leading-relaxed\">\n                Professional mobile drug and DNA testing services. \n                We bring certified, accurate testing directly to your location.\n              </p>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-neutral-300\">\n                <IconPhone className=\"w-5 h-5 mr-3 text-primary-400\" />\n                <span>(*************</span>\n              </div>\n              <div className=\"flex items-center text-neutral-300\">\n                <IconMail className=\"w-5 h-5 mr-3 text-primary-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center text-neutral-300\">\n                <IconMapPin className=\"w-5 h-5 mr-3 text-primary-400\" />\n                <span>Metro Area & Surrounding</span>\n              </div>\n              <div className=\"flex items-center text-neutral-300\">\n                <IconClock className=\"w-5 h-5 mr-3 text-primary-400\" />\n                <span>24/7 Emergency Service</span>\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex space-x-4 mt-6\">\n              {socialLinks.map((social) => (\n                <motion.a\n                  key={social.name}\n                  href={social.href}\n                  className=\"p-2 rounded-lg bg-neutral-800 text-neutral-400 hover:bg-primary-500 hover:text-white transition-all duration-300\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {social.icon}\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.1, ease: \"easeOut\" }}\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Quick Links</h4>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-neutral-400 hover:text-primary-400 transition-colors duration-300\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Services */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.2, ease: \"easeOut\" }}\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Our Services</h4>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-neutral-400 hover:text-secondary-400 transition-colors duration-300\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Newsletter & Certifications */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.3, ease: \"easeOut\" }}\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Stay Updated</h4>\n            <p className=\"text-neutral-400 mb-4\">\n              Get updates on new services and industry news.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-2 mb-6\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 rounded-lg bg-neutral-800 border border-neutral-700 text-white placeholder-neutral-500 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200\"\n              />\n              <Button\n                variant=\"primary\"\n                size=\"sm\"\n                className=\"whitespace-nowrap\"\n              >\n                Subscribe\n              </Button>\n            </div>\n\n            {/* Certifications */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-neutral-300\">\n                <IconShieldCheck className=\"w-5 h-5 mr-3 text-secondary-400\" />\n                <span className=\"text-sm\">DOT Certified</span>\n              </div>\n              <div className=\"flex items-center text-neutral-300\">\n                <IconCertificate className=\"w-5 h-5 mr-3 text-secondary-400\" />\n                <span className=\"text-sm\">SAMHSA Guidelines</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-neutral-800\">\n        <div className=\"container mx-auto px-6 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-neutral-400 text-sm mb-4 md:mb-0\">\n              © 2024 Kalanis Express. All rights reserved.\n            </div>\n            \n            <div className=\"flex flex-wrap items-center gap-6\">\n              {legalLinks.map((link) => (\n                <a\n                  key={link.name}\n                  href={link.href}\n                  className=\"text-neutral-400 hover:text-primary-400 text-sm transition-colors duration-300\"\n                >\n                  {link.name}\n                </a>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll to Top Button */}\n      <motion.button\n        onClick={scrollToTop}\n        className=\"fixed bottom-8 right-8 p-3 bg-primary-500 text-white rounded-full shadow-luxury hover:bg-primary-600 transition-all duration-300 z-50\"\n        initial={{ opacity: 0, scale: 0 }}\n        animate={{ opacity: 1, scale: 1 }}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        <IconArrowUp className=\"w-6 h-6\" />\n      </motion.button>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAmBA,MAAM,SAAmB;IACvB,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,WAAW;QACf;YAAE,MAAM;YAAoB,MAAM;QAAe;QACjD;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAqB,MAAM;QAAkB;QACrD;YAAE,MAAM;YAAqB,MAAM;QAAa;QAChD;YAAE,MAAM;YAAqB,MAAM;QAAa;KACjD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAmB,MAAM;QAAkB;KACpD;IAED,MAAM,cAAc;QAClB;YAAE,oBAAM,8OAAC,wOAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAW;QAC/E;YAAE,oBAAM,8OAAC,sOAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAU;QAC7E;YAAE,oBAAM,8OAAC,wOAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAW;QAC/E;YAAE,oBAAM,8OAAC,0OAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAY;KAClF;IAED,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;;8CAE7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAa;;;;;;;;;;;;sDAE/B,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAOvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0NAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAK;sDAEvB,OAAO,IAAI;2CANP,OAAO,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;;8CAEzD,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;;8CAEzD,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,IAAI;;;;;;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;sCAa3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;;8CAEzD,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwC;;;;;;0CAIvD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAa1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;0BAEvB,cAAA,8OAAC,4NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI/B;uCAEe", "debugId": null}}]}