@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Utilities */
@layer utilities {
  .text-gradient-luxury {
    @apply bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 bg-clip-text text-transparent;
  }

  .shadow-luxury {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .shadow-luxury-lg {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  .shadow-glow-gold {
    box-shadow: 0 0 20px rgba(234, 179, 8, 0.3);
  }
}

/* Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}


