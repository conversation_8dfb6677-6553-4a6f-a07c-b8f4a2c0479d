import type { Metada<PERSON> } from "next";
import { Inter, Playfair_Display, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains-mono",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Kalanis Express - Professional Mobile Drug & DNA Testing Services",
  description: "DOT compliant mobile drug and DNA testing services. We come to you with certified technicians, accurate results, and convenient scheduling. Serving businesses and individuals with professional health diagnostic services.",
  keywords: [
    "mobile drug testing",
    "DNA testing",
    "DOT compliance",
    "drug screening",
    "mobile testing services",
    "certified technicians",
    "workplace testing",
    "paternity testing",
    "health diagnostics"
  ],
  authors: [{ name: "Kalanis Express" }],
  creator: "Kalanis Express",
  publisher: "Kalanis Express",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://kalanisexpress.com",
    title: "Kalanis Express - Professional Mobile Drug & DNA Testing",
    description: "DOT compliant mobile drug and DNA testing services. We come to you with certified technicians and accurate results.",
    siteName: "Kalanis Express",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Kalanis Express - Mobile Testing Services",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Kalanis Express - Professional Mobile Drug & DNA Testing",
    description: "DOT compliant mobile drug and DNA testing services. We come to you with certified technicians and accurate results.",
    images: ["/og-image.jpg"],
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable} ${jetbrainsMono.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#0ea5e9" />
      </head>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
