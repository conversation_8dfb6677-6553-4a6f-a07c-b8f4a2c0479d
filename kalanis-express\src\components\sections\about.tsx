"use client";

import React from "react";
import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BackgroundGradient } from "@/components/ui/background-gradient";
import {
  IconShieldCheck,
  IconCertificate,
  IconUsers,
  IconTruck,
  IconClock,
  IconHeart,
  IconTarget,
  IconAward,
  IconPhone
} from "@tabler/icons-react";

const About: React.FC = () => {
  const values = [
    {
      icon: <IconShieldCheck className="w-8 h-8" />,
      title: "Accuracy",
      description: "Medical-grade precision in every test we perform, ensuring reliable results you can trust."
    },
    {
      icon: <IconHeart className="w-8 h-8" />,
      title: "Compassion",
      description: "Understanding that testing can be stressful, we provide caring, professional service."
    },
    {
      icon: <IconClock className="w-8 h-8" />,
      title: "Convenience",
      description: "Mobile service that comes to you, saving time and reducing stress for our clients."
    },
    {
      icon: <IconCertificate className="w-8 h-8" />,
      title: "Compliance",
      description: "Strict adherence to DOT regulations and legal requirements for defensible results."
    }
  ];

  const achievements = [
    {
      icon: <IconAward className="w-6 h-6" />,
      title: "Industry Recognition",
      description: "Certified by leading health organizations"
    },
    {
      icon: <IconUsers className="w-6 h-6" />,
      title: "Experienced Team",
      description: "Over 50 years combined experience"
    },
    {
      icon: <IconTarget className="w-6 h-6" />,
      title: "Proven Results",
      description: "99.9% accuracy rate across all testing"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-4xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-secondary-100 text-secondary-700 text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <IconHeart className="w-4 h-4 mr-2" />
            About Kalanis Express
          </motion.div>
          
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="text-gradient-luxury">Professional</span>
            <br />
            <span className="text-neutral-900">Testing Excellence</span>
          </h2>
          
          <p className="text-xl text-neutral-600 leading-relaxed">
            Founded on the principles of accuracy, convenience, and compassion, 
            Kalanis Express brings professional testing services directly to you.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Column - Story */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <h3 className="text-3xl font-bold text-neutral-900">
              Our Story
            </h3>
            
            <div className="space-y-4 text-lg text-neutral-600 leading-relaxed">
              <p>
                Kalanis Express was born from a simple observation: traditional testing 
                services were inconvenient, time-consuming, and often stressful for both 
                businesses and individuals.
              </p>
              
              <p>
                Our founder, Sylonda, recognized the need for a more convenient approach. 
                With years of experience in healthcare and a passion for helping others, 
                she created a mobile testing service that brings professional, accurate 
                testing directly to your location.
              </p>
              
              <p>
                Today, we serve hundreds of businesses and individuals across the region, 
                providing DOT-compliant drug testing, accurate DNA testing, and exceptional 
                customer service that puts your needs first.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button
                size="lg"
                variant="primary"
                icon={<IconPhone className="w-5 h-5" />}
              >
                Contact Us
              </Button>
              <Button
                size="lg"
                variant="outline"
              >
                Learn More
              </Button>
            </div>
          </motion.div>

          {/* Right Column - Achievements */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
          >
            <BackgroundGradient>
              <Card variant="luxury" className="p-8">
                <CardContent className="space-y-8">
                  <h3 className="text-2xl font-bold text-neutral-900 text-center">
                    Why Choose Us
                  </h3>
                  
                  {achievements.map((achievement, index) => (
                    <motion.div
                      key={achievement.title}
                      className="flex items-start space-x-4"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ 
                        duration: 0.6, 
                        delay: 0.4 + index * 0.1,
                        ease: "easeOut" 
                      }}
                    >
                      <div className="p-3 rounded-2xl bg-primary-100 text-primary-600 flex-shrink-0">
                        {achievement.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold text-neutral-900 mb-1">
                          {achievement.title}
                        </h4>
                        <p className="text-neutral-600">
                          {achievement.description}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </CardContent>
              </Card>
            </BackgroundGradient>
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h3 className="text-3xl font-bold text-neutral-900 mb-4">
            Our Core Values
          </h3>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            These principles guide everything we do and ensure exceptional service for every client
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut" 
              }}
            >
              <Card hover className="text-center h-full">
                <CardContent className="p-8">
                  <div className="p-4 rounded-2xl bg-secondary-100 text-secondary-600 w-fit mx-auto mb-6">
                    {value.icon}
                  </div>
                  <h4 className="text-xl font-semibold text-neutral-900 mb-4">
                    {value.title}
                  </h4>
                  <p className="text-neutral-600 leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default About;
