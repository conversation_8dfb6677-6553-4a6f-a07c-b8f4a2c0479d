"use client";

import React from "react";
import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BackgroundGradient } from "@/components/ui/background-gradient";
import {
  IconPhone,
  IconMail,
  IconMapPin,
  IconClock,
  IconCalendar,
  IconSend,
  IconShieldCheck,
  IconCertificate
} from "@tabler/icons-react";

const Contact: React.FC = () => {
  const contactInfo = [
    {
      icon: <IconPhone className="w-6 h-6" />,
      title: "Call Us",
      info: "(*************",
      description: "24/7 Support Available",
      action: "tel:+15551234567"
    },
    {
      icon: <IconMail className="w-6 h-6" />,
      title: "Email Us",
      info: "<EMAIL>",
      description: "Quick Response Guaranteed",
      action: "mailto:<EMAIL>"
    },
    {
      icon: <IconMapPin className="w-6 h-6" />,
      title: "Service Area",
      info: "Metro Area & Surrounding",
      description: "Mobile Service Available",
      action: null
    },
    {
      icon: <IconClock className="w-6 h-6" />,
      title: "Hours",
      info: "24/7 Emergency Service",
      description: "Flexible Scheduling",
      action: null
    }
  ];

  const services = [
    "DOT Drug Testing",
    "DNA Testing",
    "Mobile Collection",
    "Same-Day Service",
    "Court-Ordered Testing",
    "Workplace Testing"
  ];

  return (
    <section id="contact" className="py-24 bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-4xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <IconPhone className="w-4 h-4 mr-2" />
            Get In Touch
          </motion.div>
          
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="text-neutral-900">Ready to</span>
            <br />
            <span className="text-gradient-luxury">Get Started?</span>
          </h2>
          
          <p className="text-xl text-neutral-600 leading-relaxed">
            Contact us today to schedule your testing appointment or learn more 
            about our professional mobile testing services.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 mb-16">
          {/* Left Column - Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <BackgroundGradient>
              <Card variant="luxury" className="p-8">
                <CardContent>
                  <h3 className="text-2xl font-bold text-neutral-900 mb-6">
                    Schedule Your Testing
                  </h3>
                  
                  <form className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          First Name
                        </label>
                        <input
                          type="text"
                          className="w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200"
                          placeholder="John"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Last Name
                        </label>
                        <input
                          type="text"
                          className="w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200"
                          placeholder="Doe"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        className="w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        className="w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200"
                        placeholder="(*************"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Service Needed
                      </label>
                      <select className="w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200">
                        <option>Select a service</option>
                        <option>DOT Drug Testing</option>
                        <option>DNA Testing</option>
                        <option>Mobile Collection</option>
                        <option>Other</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Message
                      </label>
                      <textarea
                        rows={4}
                        className="w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200"
                        placeholder="Tell us about your testing needs..."
                      />
                    </div>
                    
                    <Button
                      size="lg"
                      variant="primary"
                      className="w-full"
                      icon={<IconSend className="w-5 h-5" />}
                    >
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </BackgroundGradient>
          </motion.div>

          {/* Right Column - Contact Info */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
          >
            <div>
              <h3 className="text-2xl font-bold text-neutral-900 mb-6">
                Contact Information
              </h3>
              
              <div className="space-y-4">
                {contactInfo.map((contact, index) => (
                  <motion.div
                    key={contact.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ 
                      duration: 0.6, 
                      delay: 0.4 + index * 0.1,
                      ease: "easeOut" 
                    }}
                  >
                    <Card hover className="p-6">
                      <CardContent className="flex items-center space-x-4">
                        <div className="p-3 rounded-2xl bg-primary-100 text-primary-600 flex-shrink-0">
                          {contact.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-neutral-900">
                            {contact.title}
                          </h4>
                          <p className="text-lg text-primary-600 font-medium">
                            {contact.info}
                          </p>
                          <p className="text-sm text-neutral-600">
                            {contact.description}
                          </p>
                        </div>
                        {contact.action && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(contact.action!, '_self')}
                          >
                            Contact
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div>
              <h4 className="text-xl font-bold text-neutral-900 mb-4">
                Quick Actions
              </h4>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  variant="primary"
                  icon={<IconCalendar className="w-5 h-5" />}
                  className="flex-1"
                >
                  Schedule Now
                </Button>
                <Button
                  size="lg"
                  variant="secondary"
                  icon={<IconPhone className="w-5 h-5" />}
                  className="flex-1"
                >
                  Call Now
                </Button>
              </div>
            </div>

            {/* Services List */}
            <div>
              <h4 className="text-xl font-bold text-neutral-900 mb-4">
                Our Services
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {services.map((service) => (
                  <div
                    key={service}
                    className="flex items-center text-neutral-600"
                  >
                    <IconShieldCheck className="w-4 h-4 text-secondary-500 mr-2 flex-shrink-0" />
                    <span className="text-sm">{service}</span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Emergency Contact CTA */}
        <motion.div
          className="text-center bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl p-8 lg:p-12 text-white"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <IconCertificate className="w-16 h-16 mx-auto mb-6 opacity-80" />
          <h3 className="text-3xl lg:text-4xl font-bold mb-4">
            Need Emergency Testing?
          </h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            We provide 24/7 emergency testing services for urgent situations. 
            Call now for immediate assistance.
          </p>
          <Button
            size="lg"
            variant="accent"
            icon={<IconPhone className="w-5 h-5" />}
            className="bg-white text-primary-600 hover:bg-neutral-100"
          >
            Emergency Line: (*************
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
