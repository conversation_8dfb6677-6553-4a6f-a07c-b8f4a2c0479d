"use client";

import React from "react";
import { motion } from "motion/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { BackgroundGradient } from "@/components/ui/background-gradient";
import { 
  IconShieldCheck, 
  IconClock, 
  IconMapPin, 
  IconCertificate,
  IconPhone,
  IconCalendar
} from "@tabler/icons-react";

const Hero: React.FC = () => {
  const heroText = "Professional Mobile Drug & DNA Testing Services - We Come to You";
  const subText = "DOT Compliant • Certified Technicians • Accurate Results • Convenient Scheduling";

  const features = [
    {
      icon: <IconMapPin className="w-6 h-6" />,
      title: "Mobile Service",
      description: "We come to your location"
    },
    {
      icon: <IconShieldCheck className="w-6 h-6" />,
      title: "DOT Certified",
      description: "Fully compliant testing"
    },
    {
      icon: <IconClock className="w-6 h-6" />,
      title: "Fast Results",
      description: "Quick turnaround times"
    },
    {
      icon: <IconCertificate className="w-6 h-6" />,
      title: "Accurate Testing",
      description: "Medical-grade precision"
    }
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />
      
      {/* Floating Elements */}
      <motion.div
        className="absolute top-20 left-10 w-72 h-72 bg-primary-500/10 rounded-full blur-3xl"
        animate={{
          x: [0, 100, 0],
          y: [0, -50, 0],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      
      <motion.div
        className="absolute bottom-20 right-10 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl"
        animate={{
          x: [0, -80, 0],
          y: [0, 60, 0],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <div className="container mx-auto px-6 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Badge */}
            <motion.div
              className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <IconShieldCheck className="w-4 h-4 mr-2" />
              Trusted by 500+ Businesses
            </motion.div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                <span className="text-gradient-luxury">Kalanis</span>
                <br />
                <span className="text-neutral-900">Express</span>
              </h1>
              
              <div className="text-xl lg:text-2xl text-neutral-600 max-w-2xl">
                <TextGenerateEffect 
                  words={heroText}
                  className="text-xl lg:text-2xl text-neutral-600"
                />
              </div>
            </div>

            {/* Subtitle */}
            <motion.p
              className="text-lg text-neutral-500 max-w-xl leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              {subText}
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.6 }}
            >
              <Button
                size="lg"
                variant="primary"
                icon={<IconCalendar className="w-5 h-5" />}
                className="text-lg px-8 py-4"
              >
                Schedule Testing
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                icon={<IconPhone className="w-5 h-5" />}
                className="text-lg px-8 py-4"
              >
                Call Now: (*************
              </Button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              className="flex items-center space-x-6 pt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.6 }}
            >
              <div className="text-sm text-neutral-500">
                <div className="font-semibold text-neutral-900">24/7</div>
                <div>Available</div>
              </div>
              <div className="w-px h-8 bg-neutral-300" />
              <div className="text-sm text-neutral-500">
                <div className="font-semibold text-neutral-900">Same Day</div>
                <div>Service</div>
              </div>
              <div className="w-px h-8 bg-neutral-300" />
              <div className="text-sm text-neutral-500">
                <div className="font-semibold text-neutral-900">100%</div>
                <div>Compliant</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Features Grid */}
          <motion.div
            className="grid grid-cols-2 gap-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}
              >
                <BackgroundGradient className="p-6 rounded-3xl">
                  <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 h-full">
                    <div className="flex flex-col items-center text-center space-y-4">
                      <div className="p-3 rounded-2xl bg-primary-100 text-primary-600">
                        {feature.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-neutral-900 mb-2">
                          {feature.title}
                        </h3>
                        <p className="text-sm text-neutral-600">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </BackgroundGradient>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5, duration: 0.6 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-neutral-400 rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
