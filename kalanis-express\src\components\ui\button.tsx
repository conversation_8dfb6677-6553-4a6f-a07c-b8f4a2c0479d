"use client";

import React from "react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "accent" | "outline" | "ghost";
  size?: "sm" | "md" | "lg" | "xl";
  children: React.ReactNode;
  className?: string;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      children,
      className,
      loading = false,
      icon,
      iconPosition = "left",
      disabled,
      ...props
    },
    ref
  ) => {
    const baseClasses = cn(
      "relative inline-flex items-center justify-center font-semibold transition-all duration-300 ease-out",
      "focus:outline-none focus:ring-4 focus:ring-primary-500/20",
      "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
      "active:scale-95"
    );

    const variantClasses = {
      primary: cn(
        "bg-gradient-to-r from-primary-500 to-primary-600 text-white",
        "hover:from-primary-600 hover:to-primary-700",
        "shadow-luxury hover:shadow-luxury-lg hover:shadow-glow",
        "hover:scale-105"
      ),
      secondary: cn(
        "bg-gradient-to-r from-secondary-500 to-secondary-600 text-white",
        "hover:from-secondary-600 hover:to-secondary-700",
        "shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-green",
        "hover:scale-105"
      ),
      accent: cn(
        "bg-gradient-to-r from-accent-500 to-accent-600 text-white",
        "hover:from-accent-600 hover:to-accent-700",
        "shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-gold",
        "hover:scale-105"
      ),
      outline: cn(
        "border-2 border-primary-500 text-primary-500 bg-transparent",
        "hover:bg-primary-500 hover:text-white",
        "hover:shadow-glow"
      ),
      ghost: cn(
        "text-primary-500 bg-transparent",
        "hover:bg-primary-50 hover:text-primary-600"
      ),
    };

    const sizeClasses = {
      sm: "px-4 py-2 text-sm rounded-xl",
      md: "px-6 py-3 text-base rounded-2xl",
      lg: "px-8 py-4 text-lg rounded-2xl",
      xl: "px-10 py-5 text-xl rounded-3xl",
    };

    const buttonClasses = cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      className
    );

    return (
      <motion.button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        whileHover={{ scale: disabled ? 1 : 1.05 }}
        whileTap={{ scale: disabled ? 1 : 0.95 }}
        {...props}
      >
        {loading && (
          <motion.div
            className="mr-2"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </motion.div>
        )}
        
        {icon && iconPosition === "left" && !loading && (
          <span className="mr-2">{icon}</span>
        )}
        
        <span>{children}</span>
        
        {icon && iconPosition === "right" && !loading && (
          <span className="ml-2">{icon}</span>
        )}
      </motion.button>
    );
  }
);

Button.displayName = "Button";

export { Button };
export type { ButtonProps };
