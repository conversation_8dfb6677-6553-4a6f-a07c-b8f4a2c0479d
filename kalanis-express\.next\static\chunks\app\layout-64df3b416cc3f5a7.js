(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155),s=r(2115),i=r(182),n=r(9434);let l=s.forwardRef((e,t)=>{let{variant:r="primary",size:s="md",children:l,className:o,loading:c=!1,icon:d,iconPosition:h="left",disabled:m,...x}=e,u=(0,n.cn)("relative inline-flex items-center justify-center font-semibold transition-all duration-300 ease-out","focus:outline-none focus:ring-4 focus:ring-primary-500/20","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100","active:scale-95"),p={primary:(0,n.cn)("bg-gradient-to-r from-primary-500 to-primary-600 text-white","hover:from-primary-600 hover:to-primary-700","shadow-luxury hover:shadow-luxury-lg hover:shadow-glow","hover:scale-105"),secondary:(0,n.cn)("bg-gradient-to-r from-secondary-500 to-secondary-600 text-white","hover:from-secondary-600 hover:to-secondary-700","shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-green","hover:scale-105"),accent:(0,n.cn)("bg-gradient-to-r from-accent-500 to-accent-600 text-white","hover:from-accent-600 hover:to-accent-700","shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-gold","hover:scale-105"),outline:(0,n.cn)("border-2 border-primary-500 text-primary-500 bg-transparent","hover:bg-primary-500 hover:text-white","hover:shadow-glow"),ghost:(0,n.cn)("text-primary-500 bg-transparent","hover:bg-primary-50 hover:text-primary-600")},v=(0,n.cn)(u,p[r],{sm:"px-4 py-2 text-sm rounded-xl",md:"px-6 py-3 text-base rounded-2xl",lg:"px-8 py-4 text-lg rounded-2xl",xl:"px-10 py-5 text-xl rounded-3xl"}[s],o);return(0,a.jsxs)(i.P.button,{ref:t,className:v,disabled:m||c,whileHover:{scale:m?1:1.05},whileTap:{scale:m?1:.95},...x,children:[c&&(0,a.jsx)(i.P.div,{className:"mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),d&&"left"===h&&!c&&(0,a.jsx)("span",{className:"mr-2",children:d}),(0,a.jsx)("span",{children:l}),d&&"right"===h&&!c&&(0,a.jsx)("span",{className:"ml-2",children:d})]})});l.displayName="Button"},347:()=>{},1896:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(5155),s=r(2115),i=r(2810);function n(e){let{children:t}=e;return(0,s.useEffect)(()=>{let e=new i.A({duration:1.2,easing:e=>Math.min(1,1.001-Math.pow(2,-10*e)),direction:"vertical",gestureDirection:"vertical",smooth:!0,mouseMultiplier:1,smoothTouch:!1,touchMultiplier:2,infinite:!1});return requestAnimationFrame(function t(r){e.raf(r),requestAnimationFrame(t)}),()=>{e.destroy()}},[]),(0,a.jsx)(a.Fragment,{children:t})}},4407:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var a=r(5155),s=r(2115),i=r(182),n=r(760),l=r(285),o=r(6855),c=r(8497),d=r(4395),h=r(4382),m=r(7394);let x=()=>{let[e,t]=(0,s.useState)(!1),[r,x]=(0,s.useState)(!1),u=[{name:"Home",href:"#home"},{name:"Services",href:"#services"},{name:"Process",href:"#process"},{name:"About",href:"#about"},{name:"Contact",href:"#contact"}];(0,s.useEffect)(()=>{let e=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let p=e=>{let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth"}),x(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.P.nav,{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(e?"bg-white/80 backdrop-blur-xl border-b border-neutral-200/50 shadow-luxury":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.8,ease:"easeOut"},children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-20",children:[(0,a.jsx)(i.P.div,{className:"flex items-center",whileHover:{scale:1.05},transition:{duration:.2},children:(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Kalanis"}),(0,a.jsx)("span",{className:"ml-1 ".concat(e?"text-neutral-900":"text-white"),children:"Express"})]})}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:u.map(t=>(0,a.jsx)(i.P.button,{onClick:()=>p(t.href),className:"text-sm font-medium transition-colors duration-300 hover:text-primary-500 ".concat(e?"text-neutral-700":"text-white/90"),whileHover:{scale:1.05},whileTap:{scale:.95},children:t.name},t.name))}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",icon:(0,a.jsx)(o.A,{className:"w-4 h-4"}),className:"".concat(e?"border-primary-500 text-primary-500":"border-white text-white hover:bg-white hover:text-primary-500"),children:"Call Now"}),(0,a.jsx)(l.$,{variant:"primary",size:"sm",icon:(0,a.jsx)(c.A,{className:"w-4 h-4"}),children:"Schedule"})]}),(0,a.jsx)(i.P.button,{className:"lg:hidden p-2 rounded-lg transition-colors duration-300 ".concat(e?"text-neutral-700 hover:bg-neutral-100":"text-white hover:bg-white/10"),onClick:()=>x(!r),whileTap:{scale:.95},children:r?(0,a.jsx)(d.A,{className:"w-6 h-6"}):(0,a.jsx)(h.A,{className:"w-6 h-6"})})]})})}),(0,a.jsx)(n.N,{children:r&&(0,a.jsxs)(i.P.div,{className:"fixed inset-0 z-40 lg:hidden",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:()=>x(!1)}),(0,a.jsx)(i.P.div,{className:"absolute top-20 left-6 right-6 bg-white rounded-3xl shadow-luxury overflow-hidden",initial:{opacity:0,y:-20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:.3,ease:"easeOut"},children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6 p-4 bg-primary-50 rounded-2xl",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-primary-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary-700",children:"DOT Certified & Trusted"})]}),(0,a.jsx)("div",{className:"space-y-2 mb-6",children:u.map((e,t)=>(0,a.jsx)(i.P.button,{onClick:()=>p(e.href),className:"w-full text-left p-4 rounded-2xl text-neutral-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t,duration:.3},whileTap:{scale:.98},children:e.name},e.name))}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(l.$,{variant:"outline",size:"lg",icon:(0,a.jsx)(o.A,{className:"w-5 h-5"}),className:"w-full",onClick:()=>x(!1),children:"Call Now: (*************"}),(0,a.jsx)(l.$,{variant:"primary",size:"lg",icon:(0,a.jsx)(c.A,{className:"w-5 h-5"}),className:"w-full",onClick:()=>x(!1),children:"Schedule Testing"})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-neutral-200",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"24/7 Emergency Service Available"}),(0,a.jsx)("p",{className:"text-xs text-neutral-500",children:"DOT Compliant • Mobile Service • Fast Results"})]})})]})})]})})]})}},6484:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6707,23)),Promise.resolve().then(r.t.bind(r,5586,23)),Promise.resolve().then(r.t.bind(r,786,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,4407)),Promise.resolve().then(r.bind(r,1896))},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(2596),s=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[521,182,849,11,441,684,358],()=>t(6484)),_N_E=e.O()}]);