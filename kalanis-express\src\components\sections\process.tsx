"use client";

import React from "react";
import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  IconCalendar,
  IconMapPin,
  IconFlask,
  IconFileText,
  IconPhone,
  IconClock,
  IconShieldCheck,
  IconArrowRight
} from "@tabler/icons-react";

const Process: React.FC = () => {
  const steps = [
    {
      step: "01",
      icon: <IconCalendar className="w-8 h-8" />,
      title: "Schedule Online",
      description: "Book your appointment through our easy online system or call our 24/7 support line. Choose a time that works for you.",
      details: [
        "Online booking system",
        "Flexible scheduling",
        "Same-day availability",
        "24/7 customer support"
      ],
      color: "primary"
    },
    {
      step: "02",
      icon: <IconMapPin className="w-8 h-8" />,
      title: "We Come to You",
      description: "Our certified technician arrives at your chosen location with all necessary equipment and documentation.",
      details: [
        "Mobile service anywhere",
        "Certified technicians",
        "Professional equipment",
        "Proper documentation"
      ],
      color: "secondary"
    },
    {
      step: "03",
      icon: <IconFlask className="w-8 h-8" />,
      title: "Professional Collection",
      description: "Quick, professional sample collection following strict protocols to ensure accuracy and compliance.",
      details: [
        "Chain of custody",
        "Sterile procedures",
        "Quick collection",
        "Privacy maintained"
      ],
      color: "accent"
    },
    {
      step: "04",
      icon: <IconFileText className="w-8 h-8" />,
      title: "Fast Results",
      description: "Receive accurate results quickly through our secure portal or direct communication as preferred.",
      details: [
        "Secure result delivery",
        "Fast turnaround",
        "Multiple delivery options",
        "Legal documentation"
      ],
      color: "primary"
    }
  ];

  const benefits = [
    {
      icon: <IconClock className="w-6 h-6" />,
      title: "Save Time",
      description: "No travel required - we come to your location"
    },
    {
      icon: <IconShieldCheck className="w-6 h-6" />,
      title: "Guaranteed Accuracy",
      description: "Medical-grade testing with certified technicians"
    },
    {
      icon: <IconPhone className="w-6 h-6" />,
      title: "24/7 Support",
      description: "Round-the-clock customer service and scheduling"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-4xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-secondary-100 text-secondary-700 text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <IconClock className="w-4 h-4 mr-2" />
            Simple Process
          </motion.div>
          
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="text-neutral-900">How It</span>
            <br />
            <span className="text-gradient-luxury">Works</span>
          </h2>
          
          <p className="text-xl text-neutral-600 leading-relaxed">
            Our streamlined process makes testing convenient and stress-free. 
            From booking to results, we handle everything professionally.
          </p>
        </motion.div>

        {/* Process Steps */}
        <div className="relative mb-20">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-200 via-secondary-200 to-accent-200 transform -translate-y-1/2 z-0" />
          
          <div className="grid lg:grid-cols-4 gap-8 relative z-10">
            {steps.map((step, index) => (
              <motion.div
                key={step.step}
                className="relative"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ 
                  duration: 0.8, 
                  delay: index * 0.2,
                  ease: "easeOut" 
                }}
              >
                <Card variant="luxury" className="text-center h-full group">
                  <CardContent className="p-8">
                    {/* Step Number */}
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                        step.color === 'primary' ? 'bg-primary-500' :
                        step.color === 'secondary' ? 'bg-secondary-500' :
                        'bg-accent-500'
                      }`}>
                        {step.step}
                      </div>
                    </div>

                    {/* Icon */}
                    <div className={`p-4 rounded-2xl w-fit mx-auto mb-6 mt-4 ${
                      step.color === 'primary' ? 'bg-primary-100 text-primary-600' :
                      step.color === 'secondary' ? 'bg-secondary-100 text-secondary-600' :
                      'bg-accent-100 text-accent-600'
                    }`}>
                      {step.icon}
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-bold text-neutral-900 mb-4">
                      {step.title}
                    </h3>
                    
                    <p className="text-neutral-600 mb-6 leading-relaxed">
                      {step.description}
                    </p>

                    {/* Details */}
                    <ul className="space-y-2 text-sm text-neutral-500">
                      {step.details.map((detail, idx) => (
                        <li key={idx} className="flex items-center justify-center">
                          <IconShieldCheck className="w-3 h-3 text-secondary-500 mr-2 flex-shrink-0" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Benefits Section */}
        <motion.div
          className="grid md:grid-cols-3 gap-8 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut" 
              }}
            >
              <div className="p-4 rounded-2xl bg-primary-100 text-primary-600 w-fit mx-auto mb-4">
                {benefit.icon}
              </div>
              <h4 className="text-lg font-semibold text-neutral-900 mb-2">
                {benefit.title}
              </h4>
              <p className="text-neutral-600">
                {benefit.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-8 lg:p-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h3 className="text-3xl lg:text-4xl font-bold text-neutral-900 mb-4">
            Ready to Get Started?
          </h3>
          <p className="text-xl text-neutral-600 mb-8 max-w-2xl mx-auto">
            Schedule your testing appointment today and experience the convenience 
            of professional mobile testing services.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              variant="primary"
              icon={<IconCalendar className="w-5 h-5" />}
            >
              Schedule Testing
            </Button>
            <Button
              size="lg"
              variant="outline"
              icon={<IconArrowRight className="w-5 h-5" />}
              iconPosition="right"
            >
              View Pricing
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Process;
