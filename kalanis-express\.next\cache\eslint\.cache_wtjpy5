[{"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\layout.tsx": "1", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\loading.tsx": "2", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\page.tsx": "3", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\navigation\\navbar.tsx": "4", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\providers\\smooth-scroll-provider.tsx": "5", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\about.tsx": "6", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\contact.tsx": "7", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\footer.tsx": "8", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\hero.tsx": "9", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\process.tsx": "10", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\services.tsx": "11", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\testimonials.tsx": "12", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\animated-tooltip.tsx": "13", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\background-gradient.tsx": "14", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\button.tsx": "15", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\card.tsx": "16", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\text-generate-effect.tsx": "17", "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\lib\\utils.ts": "18"}, {"size": 3244, "mtime": 1751044181881, "results": "19", "hashOfConfig": "20"}, {"size": 1604, "mtime": 1751044259374, "results": "21", "hashOfConfig": "20"}, {"size": 597, "mtime": 1751044099334, "results": "22", "hashOfConfig": "20"}, {"size": 7878, "mtime": 1751044161805, "results": "23", "hashOfConfig": "20"}, {"size": 784, "mtime": 1751044130757, "results": "24", "hashOfConfig": "20"}, {"size": 9019, "mtime": 1751047705961, "results": "25", "hashOfConfig": "20"}, {"size": 12543, "mtime": 1751047721799, "results": "26", "hashOfConfig": "20"}, {"size": 8802, "mtime": 1751044088639, "results": "27", "hashOfConfig": "20"}, {"size": 8208, "mtime": 1751044195170, "results": "28", "hashOfConfig": "20"}, {"size": 9319, "mtime": 1751044218297, "results": "29", "hashOfConfig": "20"}, {"size": 10024, "mtime": 1751047772963, "results": "30", "hashOfConfig": "20"}, {"size": 10839, "mtime": 1751047853193, "results": "31", "hashOfConfig": "20"}, {"size": 3233, "mtime": 1751043519703, "results": "32", "hashOfConfig": "20"}, {"size": 2533, "mtime": 1751043547105, "results": "33", "hashOfConfig": "20"}, {"size": 3804, "mtime": 1751043476663, "results": "34", "hashOfConfig": "20"}, {"size": 5152, "mtime": 1751043501289, "results": "35", "hashOfConfig": "20"}, {"size": 1484, "mtime": 1751043531974, "results": "36", "hashOfConfig": "20"}, {"size": 166, "mtime": 1751043349603, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1guttmp", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\layout.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\loading.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\page.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\navigation\\navbar.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\providers\\smooth-scroll-provider.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\about.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\contact.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\footer.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\hero.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\process.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\services.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\testimonials.tsx", ["92", "93", "94"], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\animated-tooltip.tsx", ["95", "96"], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\background-gradient.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\button.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\card.tsx", [], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\ui\\text-generate-effect.tsx", ["97"], [], "D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\lib\\utils.ts", [], [], {"ruleId": "98", "severity": 2, "message": "99", "line": 222, "column": 21, "nodeType": "100", "messageId": "101", "suggestions": "102"}, {"ruleId": "98", "severity": 2, "message": "99", "line": 222, "column": 77, "nodeType": "100", "messageId": "101", "suggestions": "103"}, {"ruleId": "104", "severity": 1, "message": "105", "line": 227, "column": 21, "nodeType": "106", "endLine": 231, "endColumn": 23}, {"ruleId": "107", "severity": 2, "message": "108", "line": 52, "column": 25, "nodeType": null, "messageId": "109", "endLine": 52, "endColumn": 28}, {"ruleId": "104", "severity": 1, "message": "105", "line": 90, "column": 11, "nodeType": "106", "endLine": 97, "endColumn": 13}, {"ruleId": "110", "severity": 1, "message": "111", "line": 35, "column": 6, "nodeType": "112", "endLine": 35, "endColumn": 48, "suggestions": "113"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["114", "115", "116", "117"], ["118", "119", "120", "121"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'idx' is defined but never used.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'scope.current'. Either exclude it or remove the dependency array. Mutable values like 'scope.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["122"], {"messageId": "123", "data": "124", "fix": "125", "desc": "126"}, {"messageId": "123", "data": "127", "fix": "128", "desc": "129"}, {"messageId": "123", "data": "130", "fix": "131", "desc": "132"}, {"messageId": "123", "data": "133", "fix": "134", "desc": "135"}, {"messageId": "123", "data": "136", "fix": "137", "desc": "126"}, {"messageId": "123", "data": "138", "fix": "139", "desc": "129"}, {"messageId": "123", "data": "140", "fix": "141", "desc": "132"}, {"messageId": "123", "data": "142", "fix": "143", "desc": "135"}, {"desc": "144", "fix": "145"}, "replaceWithAlt", {"alt": "146"}, {"range": "147", "text": "148"}, "Replace with `&quot;`.", {"alt": "149"}, {"range": "150", "text": "151"}, "Replace with `&ldquo;`.", {"alt": "152"}, {"range": "153", "text": "154"}, "Replace with `&#34;`.", {"alt": "155"}, {"range": "156", "text": "157"}, "Replace with `&rdquo;`.", {"alt": "146"}, {"range": "158", "text": "159"}, {"alt": "149"}, {"range": "160", "text": "161"}, {"alt": "152"}, {"range": "162", "text": "163"}, {"alt": "155"}, {"range": "164", "text": "165"}, "Update the dependencies array to be: [animate, duration, filter]", {"range": "166", "text": "167"}, "&quot;", [7902, 7951], "\n                    &quot;                    &ldquo;", "&ldquo;", [7902, 7951], "\n                    &ldquo;                    &ldquo;", "&#34;", [7902, 7951], "\n                    &#34;                    &ldquo;", "&rdquo;", [7902, 7951], "\n                    &rdquo;                    &ldquo;", [7972, 7999], "&rdquo;&quot;\n                  ", [7972, 7999], "&rdquo;&ldquo;\n                  ", [7972, 7999], "&rdquo;&#34;\n                  ", [7972, 7999], "&rdquo;&rdquo;\n                  ", [713, 755], "[animate, duration, filter]"]