"use client";

import React from "react";
import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { AnimatedTooltip } from "@/components/ui/animated-tooltip";
import {
  IconStar,
  IconQuote,
  IconShieldCheck,
  IconCertificate,
  IconClock
} from "@tabler/icons-react";

const Testimonials: React.FC = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "HR Director",
      company: "TransLogistics Inc.",
      content: "Kalanis Express has been a game-changer for our DOT compliance. Their mobile service saves us hours of employee downtime, and their technicians are always professional and thorough.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "<PERSON>",
      role: "Safety Manager",
      company: "BuildRight Construction",
      content: "The convenience of on-site testing is incredible. We can maintain our safety standards without disrupting our work schedule. Highly recommend their services.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Jennifer <PERSON>",
      role: "Legal Assistant",
      company: "Martinez & Associates",
      content: "For court-ordered DNA testing, Kalanis Express provides the professionalism and accuracy we need. Their documentation is always perfect for legal proceedings.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "David Thompson",
      role: "Fleet Manager",
      company: "Metro Delivery Services",
      content: "Same-day service and accurate results every time. Their mobile testing has streamlined our hiring process and keeps our drivers compliant with DOT regulations.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    }
  ];

  const stats = [
    {
      number: "500+",
      label: "Businesses Served",
      icon: <IconShieldCheck className="w-6 h-6" />
    },
    {
      number: "10,000+",
      label: "Tests Completed",
      icon: <IconCertificate className="w-6 h-6" />
    },
    {
      number: "99.9%",
      label: "Accuracy Rate",
      icon: <IconStar className="w-6 h-6" />
    },
    {
      number: "24/7",
      label: "Support Available",
      icon: <IconClock className="w-6 h-6" />
    }
  ];

  const certifications = [
    {
      name: "DOT Certified",
      description: "Department of Transportation approved testing procedures"
    },
    {
      name: "SAMHSA Guidelines",
      description: "Following Substance Abuse and Mental Health Services guidelines"
    },
    {
      name: "Chain of Custody",
      description: "Strict protocols for sample handling and documentation"
    },
    {
      name: "Legal Compliance",
      description: "Court-admissible results and proper documentation"
    }
  ];

  const teamMembers = [
    {
      id: 1,
      name: "Dr. Sarah Wilson",
      designation: "Medical Director",
      image: "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face"
    },
    {
      id: 2,
      name: "Mike Rodriguez",
      designation: "Lead Technician",
      image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face"
    },
    {
      id: 3,
      name: "Lisa Chen",
      designation: "Quality Assurance",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face"
    },
    {
      id: 4,
      name: "James Parker",
      designation: "Mobile Technician",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-neutral-50 via-white to-primary-50/30">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-4xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-accent-100 text-accent-700 text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <IconStar className="w-4 h-4 mr-2" />
            Trusted by Professionals
          </motion.div>
          
          <h2 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="text-neutral-900">What Our</span>
            <br />
            <span className="text-gradient-luxury">Clients Say</span>
          </h2>
          
          <p className="text-xl text-neutral-600 leading-relaxed">
            Don&apos;t just take our word for it. See what businesses and individuals
            say about our professional testing services.
          </p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut" 
              }}
            >
              <div className="p-4 rounded-2xl bg-primary-100 text-primary-600 w-fit mx-auto mb-4">
                {stat.icon}
              </div>
              <div className="text-3xl lg:text-4xl font-bold text-neutral-900 mb-2">
                {stat.number}
              </div>
              <div className="text-neutral-600 font-medium">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          className="grid lg:grid-cols-2 gap-8 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut" 
              }}
            >
              <Card variant="luxury" className="h-full">
                <CardContent className="p-8">
                  {/* Quote Icon */}
                  <IconQuote className="w-8 h-8 text-primary-500 mb-4" />
                  
                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <IconStar key={i} className="w-5 h-5 text-accent-500 fill-current" />
                    ))}
                  </div>
                  
                  {/* Content */}
                  <p className="text-neutral-700 leading-relaxed mb-6 text-lg">
                    "                    &ldquo;{testimonial.content}&rdquo;"
                  </p>
                  
                  {/* Author */}
                  <div className="flex items-center">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <div className="font-semibold text-neutral-900">
                        {testimonial.name}
                      </div>
                      <div className="text-sm text-neutral-600">
                        {testimonial.role}, {testimonial.company}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Team Section */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h3 className="text-3xl font-bold text-neutral-900 mb-4">
            Meet Our Certified Team
          </h3>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto mb-8">
            Our experienced professionals ensure accurate results and exceptional service
          </p>
          
          <div className="flex justify-center">
            <AnimatedTooltip items={teamMembers} />
          </div>
        </motion.div>

        {/* Certifications */}
        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {certifications.map((cert, index) => (
            <motion.div
              key={cert.name}
              className="text-center p-6 rounded-2xl bg-white border border-neutral-200 hover:border-primary-300 transition-colors duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut" 
              }}
            >
              <IconShieldCheck className="w-8 h-8 text-secondary-500 mx-auto mb-3" />
              <h4 className="font-semibold text-neutral-900 mb-2">
                {cert.name}
              </h4>
              <p className="text-sm text-neutral-600">
                {cert.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
