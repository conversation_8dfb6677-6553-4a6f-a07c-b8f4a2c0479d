"use client";

import React from "react";
import { motion } from "motion/react";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  IconFlask,
  IconDna,
  IconTruck,
  IconShieldCheck,
  IconClock,
  IconCertificate,
  IconUsers,
  IconBuilding,
  IconCalendar,
  IconArrowRight
} from "@tabler/icons-react";

const Services: React.FC = () => {
  const services = [
    {
      icon: <IconFlask className="w-8 h-8" />,
      title: "DOT Drug Testing",
      description: "Department of Transportation compliant drug and alcohol testing for commercial drivers and safety-sensitive employees.",
      features: [
        "5-Panel & 10-Panel Testing",
        "Breath Alcohol Testing",
        "Random Testing Programs",
        "Return-to-Duty Testing"
      ],
      color: "primary"
    },
    {
      icon: <IconDna className="w-8 h-8" />,
      title: "DNA Testing",
      description: "Accurate paternity and relationship testing with legally defensible results for personal or court-ordered cases.",
      features: [
        "Paternity Testing",
        "Sibling DNA Testing",
        "Grandparent Testing",
        "Legal & Personal Testing"
      ],
      color: "secondary"
    },
    {
      icon: <IconTruck className="w-8 h-8" />,
      title: "Mobile Collection",
      description: "Convenient on-site collection services at your workplace, home, or any location that works for you.",
      features: [
        "Workplace Testing",
        "Home Collection",
        "Same-Day Service",
        "Flexible Scheduling"
      ],
      color: "accent"
    }
  ];

  const industries = [
    {
      icon: <IconBuilding className="w-6 h-6" />,
      title: "Transportation",
      description: "DOT compliance for trucking, aviation, and maritime companies"
    },
    {
      icon: <IconUsers className="w-6 h-6" />,
      title: "Healthcare",
      description: "Pre-employment and random testing for medical facilities"
    },
    {
      icon: <IconShieldCheck className="w-6 h-6" />,
      title: "Construction",
      description: "Safety-focused testing for construction and industrial sites"
    },
    {
      icon: <IconCertificate className="w-6 h-6" />,
      title: "Legal Services",
      description: "Court-ordered testing and legal documentation support"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="services" className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-4xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="inline-flex items-center px-6 py-3 rounded-full bg-primary-100 text-primary-700 text-sm font-semibold mb-8 shadow-sm"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <IconShieldCheck className="w-4 h-4 mr-2" />
            Professional Testing Services
          </motion.div>

          <h2 className="text-4xl lg:text-6xl font-bold mb-8">
            <span className="text-gradient-luxury">Comprehensive</span>
            <br />
            <span className="text-neutral-900">Testing Solutions</span>
          </h2>

          <p className="text-xl text-neutral-700 leading-relaxed max-w-3xl mx-auto">
            From DOT compliance to DNA testing, we provide accurate, reliable results
            with the convenience of mobile service. Our certified technicians ensure
            professional standards every time.
          </p>
        </motion.div>

        {/* Main Services Grid */}
        <motion.div
          className="grid lg:grid-cols-3 gap-8 mb-20"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {services.map((service) => (
            <motion.div key={service.title} variants={itemVariants}>
              <Card className="h-full bg-white border-2 border-neutral-100 hover:border-primary-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
                <CardHeader className="pb-4">
                  <div className="p-4 rounded-2xl w-fit mb-4 bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg">
                    {service.icon}
                  </div>
                  <CardTitle className="text-2xl font-bold text-neutral-900 mb-3">
                    {service.title}
                  </CardTitle>
                  <CardDescription className="text-base text-neutral-700 leading-relaxed">
                    {service.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <ul className="space-y-3 mb-8">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-neutral-800">
                        <div className="w-5 h-5 rounded-full bg-secondary-100 flex items-center justify-center mr-3 flex-shrink-0">
                          <IconShieldCheck className="w-3 h-3 text-secondary-600" />
                        </div>
                        <span className="font-medium">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                    icon={<IconArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />}
                    iconPosition="right"
                  >
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Industries We Serve */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h3 className="text-4xl font-bold text-neutral-900 mb-6">
            Industries We Serve
          </h3>
          <p className="text-xl text-neutral-700 max-w-3xl mx-auto leading-relaxed">
            Trusted by businesses across multiple industries for reliable testing services
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {industries.map((industry) => (
            <motion.div key={industry.title} variants={itemVariants}>
              <Card className="text-center h-full bg-white border-2 border-neutral-100 hover:border-primary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
                <CardContent className="p-8">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 text-white w-fit mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    {industry.icon}
                  </div>
                  <h4 className="font-bold text-neutral-900 mb-3 text-lg">
                    {industry.title}
                  </h4>
                  <p className="text-neutral-700 leading-relaxed">
                    {industry.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl p-8 lg:p-12 text-white">
            <h3 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Schedule Your Testing?
            </h3>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Get started with professional, convenient testing services. 
              Same-day appointments available.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="accent"
                icon={<IconCalendar className="w-5 h-5" />}
                className="bg-white text-primary-600 hover:bg-neutral-100"
              >
                Schedule Now
              </Button>
              <Button
                size="lg"
                variant="outline"
                icon={<IconClock className="w-5 h-5" />}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                24/7 Support
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
