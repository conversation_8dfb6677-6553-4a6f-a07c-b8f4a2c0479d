exports.id=945,exports.ids=[945],exports.modules={1135:()=>{},2108:(e,t,s)=>{Promise.resolve().then(s.bind(s,3587)),Promise.resolve().then(s.bind(s,8917))},2807:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var i=s(687),r=s(331);function a(){return(0,i.jsx)("div",{className:"fixed inset-0 bg-white z-50 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(r.P.div,{className:"mb-8",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6,ease:"easeOut"},children:(0,i.jsxs)("h1",{className:"text-4xl font-bold",children:[(0,i.jsx)("span",{className:"text-gradient-luxury",children:"<PERSON>lani<PERSON>"}),(0,i.jsx)("br",{}),(0,i.jsx)("span",{className:"text-neutral-900",children:"Express"})]})}),(0,i.jsx)("div",{className:"flex items-center justify-center space-x-2",children:[0,1,2].map(e=>(0,i.jsx)(r.P.div,{className:"w-3 h-3 bg-primary-500 rounded-full",animate:{scale:[1,1.2,1],opacity:[.7,1,.7]},transition:{duration:1.5,repeat:1/0,delay:.2*e,ease:"easeInOut"}},e))}),(0,i.jsx)(r.P.p,{className:"mt-6 text-neutral-600",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.6},children:"Loading professional testing services..."})]})})}},3587:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});let i=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\navigation\\\\navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\navigation\\navbar.tsx","default")},3637:(e,t,s)=>{Promise.resolve().then(s.bind(s,2807))},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>x,viewport:()=>h});var i=s(7413),r=s(1194),a=s.n(r),n=s(5411),l=s.n(n),o=s(9123),c=s.n(o);s(1135);var d=s(8917),m=s(3587);let h={width:"device-width",initialScale:1,maximumScale:1},x={title:"Kalanis Express - Professional Mobile Drug & DNA Testing Services",description:"DOT compliant mobile drug and DNA testing services. We come to you with certified technicians, accurate results, and convenient scheduling. Serving businesses and individuals with professional health diagnostic services.",keywords:["mobile drug testing","DNA testing","DOT compliance","drug screening","mobile testing services","certified technicians","workplace testing","paternity testing","health diagnostics"],authors:[{name:"Kalanis Express"}],creator:"Kalanis Express",publisher:"Kalanis Express",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:"https://kalanisexpress.com",title:"Kalanis Express - Professional Mobile Drug & DNA Testing",description:"DOT compliant mobile drug and DNA testing services. We come to you with certified technicians and accurate results.",siteName:"Kalanis Express",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Kalanis Express - Mobile Testing Services"}]},twitter:{card:"summary_large_image",title:"Kalanis Express - Professional Mobile Drug & DNA Testing",description:"DOT compliant mobile drug and DNA testing services. We come to you with certified technicians and accurate results.",images:["/og-image.jpg"]}};function p({children:e}){return(0,i.jsxs)("html",{lang:"en",className:`${a().variable} ${l().variable} ${c().variable}`,children:[(0,i.jsxs)("head",{children:[(0,i.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,i.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,i.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,i.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,i.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,i.jsx)("meta",{name:"theme-color",content:"#0ea5e9"})]}),(0,i.jsx)("body",{className:"font-sans antialiased",children:(0,i.jsxs)(d.default,{children:[(0,i.jsx)(m.default,{}),e]})})]})}},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var i=s(9384),r=s(2348);function a(...e){return(0,r.QP)((0,i.$)(e))}},5031:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},5107:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var i=s(687);function r({children:e}){return(0,i.jsx)(i.Fragment,{children:e})}s(3210),s(2970)},5660:(e,t,s)=>{Promise.resolve().then(s.bind(s,9465)),Promise.resolve().then(s.bind(s,5107))},7189:(e,t,s)=>{Promise.resolve().then(s.bind(s,7393))},7393:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\app\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\loading.tsx","default")},8175:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},8917:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});let i=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\providers\\\\smooth-scroll-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\providers\\smooth-scroll-provider.tsx","default")},9465:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var i=s(687),r=s(3210),a=s(331),n=s(8920),l=s(9523),o=s(9147),c=s(427),d=s(8843),m=s(6206),h=s(3502);let x=()=>{let[e,t]=(0,r.useState)(!1),[s,x]=(0,r.useState)(!1),p=[{name:"Home",href:"#home"},{name:"Services",href:"#services"},{name:"Process",href:"#process"},{name:"About",href:"#about"},{name:"Contact",href:"#contact"}];(0,r.useEffect)(()=>{let e=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let u=e=>{let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth"}),x(!1)};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.P.nav,{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${e?"bg-white/80 backdrop-blur-xl border-b border-neutral-200/50 shadow-luxury":"bg-transparent"}`,initial:{y:-100},animate:{y:0},transition:{duration:.8,ease:"easeOut"},children:(0,i.jsx)("div",{className:"container mx-auto px-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between h-20",children:[(0,i.jsx)(a.P.div,{className:"flex items-center",whileHover:{scale:1.05},transition:{duration:.2},children:(0,i.jsxs)("div",{className:"text-2xl font-bold",children:[(0,i.jsx)("span",{className:"text-gradient-luxury",children:"Kalanis"}),(0,i.jsx)("span",{className:`ml-1 ${e?"text-neutral-900":"text-white"}`,children:"Express"})]})}),(0,i.jsx)("div",{className:"hidden lg:flex items-center space-x-8",children:p.map(t=>(0,i.jsx)(a.P.button,{onClick:()=>u(t.href),className:`text-sm font-medium transition-colors duration-300 hover:text-primary-500 ${e?"text-neutral-700":"text-white/90"}`,whileHover:{scale:1.05},whileTap:{scale:.95},children:t.name},t.name))}),(0,i.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,i.jsx)(l.$,{variant:"outline",size:"sm",icon:(0,i.jsx)(o.A,{className:"w-4 h-4"}),className:`${e?"border-primary-500 text-primary-500":"border-white text-white hover:bg-white hover:text-primary-500"}`,children:"Call Now"}),(0,i.jsx)(l.$,{variant:"primary",size:"sm",icon:(0,i.jsx)(c.A,{className:"w-4 h-4"}),children:"Schedule"})]}),(0,i.jsx)(a.P.button,{className:`lg:hidden p-2 rounded-lg transition-colors duration-300 ${e?"text-neutral-700 hover:bg-neutral-100":"text-white hover:bg-white/10"}`,onClick:()=>x(!s),whileTap:{scale:.95},children:s?(0,i.jsx)(d.A,{className:"w-6 h-6"}):(0,i.jsx)(m.A,{className:"w-6 h-6"})})]})})}),(0,i.jsx)(n.N,{children:s&&(0,i.jsxs)(a.P.div,{className:"fixed inset-0 z-40 lg:hidden",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:()=>x(!1)}),(0,i.jsx)(a.P.div,{className:"absolute top-20 left-6 right-6 bg-white rounded-3xl shadow-luxury overflow-hidden",initial:{opacity:0,y:-20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:.3,ease:"easeOut"},children:(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center mb-6 p-4 bg-primary-50 rounded-2xl",children:[(0,i.jsx)(h.A,{className:"w-5 h-5 text-primary-500 mr-2"}),(0,i.jsx)("span",{className:"text-sm font-medium text-primary-700",children:"DOT Certified & Trusted"})]}),(0,i.jsx)("div",{className:"space-y-2 mb-6",children:p.map((e,t)=>(0,i.jsx)(a.P.button,{onClick:()=>u(e.href),className:"w-full text-left p-4 rounded-2xl text-neutral-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t,duration:.3},whileTap:{scale:.98},children:e.name},e.name))}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(l.$,{variant:"outline",size:"lg",icon:(0,i.jsx)(o.A,{className:"w-5 h-5"}),className:"w-full",onClick:()=>x(!1),children:"Call Now: (*************"}),(0,i.jsx)(l.$,{variant:"primary",size:"lg",icon:(0,i.jsx)(c.A,{className:"w-5 h-5"}),className:"w-full",onClick:()=>x(!1),children:"Schedule Testing"})]}),(0,i.jsx)("div",{className:"mt-6 pt-6 border-t border-neutral-200",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("p",{className:"text-sm text-neutral-600 mb-2",children:"24/7 Emergency Service Available"}),(0,i.jsx)("p",{className:"text-xs text-neutral-500",children:"DOT Compliant • Mobile Service • Fast Results"})]})})]})})]})})]})}},9523:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var i=s(687),r=s(3210),a=s.n(r),n=s(331),l=s(4780);let o=a().forwardRef(({variant:e="primary",size:t="md",children:s,className:r,loading:a=!1,icon:o,iconPosition:c="left",disabled:d,...m},h)=>{let x=(0,l.cn)("relative inline-flex items-center justify-center font-semibold transition-all duration-300 ease-out","focus:outline-none focus:ring-4 focus:ring-primary-500/20","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100","active:scale-95"),p={primary:(0,l.cn)("bg-gradient-to-r from-primary-500 to-primary-600 text-white","hover:from-primary-600 hover:to-primary-700","shadow-luxury hover:shadow-luxury-lg hover:shadow-glow","hover:scale-105"),secondary:(0,l.cn)("bg-gradient-to-r from-secondary-500 to-secondary-600 text-white","hover:from-secondary-600 hover:to-secondary-700","shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-green","hover:scale-105"),accent:(0,l.cn)("bg-gradient-to-r from-accent-500 to-accent-600 text-white","hover:from-accent-600 hover:to-accent-700","shadow-luxury hover:shadow-luxury-lg hover:shadow-glow-gold","hover:scale-105"),outline:(0,l.cn)("border-2 border-primary-500 text-primary-500 bg-transparent","hover:bg-primary-500 hover:text-white","hover:shadow-glow"),ghost:(0,l.cn)("text-primary-500 bg-transparent","hover:bg-primary-50 hover:text-primary-600")},u=(0,l.cn)(x,p[e],{sm:"px-4 py-2 text-sm rounded-xl",md:"px-6 py-3 text-base rounded-2xl",lg:"px-8 py-4 text-lg rounded-2xl",xl:"px-10 py-5 text-xl rounded-3xl"}[t],r);return(0,i.jsxs)(n.P.button,{ref:h,className:u,disabled:d||a,whileHover:{scale:d?1:1.05},whileTap:{scale:d?1:.95},...m,children:[a&&(0,i.jsx)(n.P.div,{className:"mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,i.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),o&&"left"===c&&!a&&(0,i.jsx)("span",{className:"mr-2",children:o}),(0,i.jsx)("span",{children:s}),o&&"right"===c&&!a&&(0,i.jsx)("span",{className:"ml-2",children:o})]})});o.displayName="Button"}};