"use client";

import React from "react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "glass" | "luxury" | "medical";
  hover?: boolean;
  padding?: "none" | "sm" | "md" | "lg" | "xl";
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      children,
      className,
      variant = "default",
      hover = true,
      padding = "md",
      ...props
    },
    ref
  ) => {
    const baseClasses = cn(
      "relative rounded-3xl transition-all duration-500 ease-out",
      "border border-neutral-200/50"
    );

    const variantClasses = {
      default: cn(
        "bg-white/80 backdrop-blur-xl",
        "shadow-luxury",
        hover && "hover:shadow-luxury-lg hover:scale-[1.02] hover:-translate-y-2"
      ),
      glass: cn(
        "bg-white/10 backdrop-blur-md border-white/20",
        "shadow-luxury",
        hover && "hover:shadow-luxury-lg hover:scale-[1.02]"
      ),
      luxury: cn(
        "bg-gradient-to-br from-white via-primary-50/30 to-secondary-50/30",
        "backdrop-blur-xl border-primary-200/30",
        "shadow-luxury",
        hover && "hover:shadow-luxury-lg hover:shadow-glow hover:scale-[1.02] hover:-translate-y-2"
      ),
      medical: cn(
        "bg-gradient-to-br from-white via-secondary-50/50 to-primary-50/30",
        "backdrop-blur-xl border-secondary-200/30",
        "shadow-luxury",
        hover && "hover:shadow-luxury-lg hover:shadow-glow-green hover:scale-[1.02] hover:-translate-y-2"
      ),
    };

    const paddingClasses = {
      none: "",
      sm: "p-4",
      md: "p-6",
      lg: "p-8",
      xl: "p-12",
    };

    const cardClasses = cn(
      baseClasses,
      variantClasses[variant],
      paddingClasses[padding],
      className
    );

    return (
      <motion.div
        ref={ref}
        className={cardClasses}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        {...props}
      >
        {/* Gradient overlay for luxury variant */}
        {variant === "luxury" && (
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-primary-50/20 to-secondary-50/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
        )}
        
        {/* Content */}
        <div className="relative z-10">{children}</div>
      </motion.div>
    );
  }
);

Card.displayName = "Card";

// Card Header Component
interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex flex-col space-y-1.5 pb-6", className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = "CardHeader";

// Card Title Component
interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  gradient?: boolean;
}

const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ children, className, gradient = false, ...props }, ref) => {
    return (
      <h3
        ref={ref}
        className={cn(
          "text-2xl font-semibold leading-none tracking-tight",
          gradient && "text-gradient-luxury",
          className
        )}
        {...props}
      >
        {children}
      </h3>
    );
  }
);

CardTitle.displayName = "CardTitle";

// Card Description Component
interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn("text-neutral-600 leading-relaxed", className)}
        {...props}
      >
        {children}
      </p>
    );
  }
);

CardDescription.displayName = "CardDescription";

// Card Content Component
interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("", className)} {...props}>
        {children}
      </div>
    );
  }
);

CardContent.displayName = "CardContent";

// Card Footer Component
interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex items-center pt-6", className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = "CardFooter";

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
};

export type {
  CardProps,
  CardHeaderProps,
  CardTitleProps,
  CardDescriptionProps,
  CardContentProps,
  CardFooterProps,
};
