(()=>{var e={};e.id=974,e.ids=[974],e.modules={21:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","clock","IconClock",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 7v5l3 3",key:"svg-1"}]])},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function a(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return a}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1080:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","mail","IconMail",[["path",{d:"M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z",key:"svg-0"}],["path",{d:"M3 7l9 6l9 -6",key:"svg-1"}]])},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(7413),i=r(3427),n=r(9579),s=r(3888),o=r(4647),l=r(3040),c=r(4295),d=r(2006);function u(){return(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)(n.default,{}),(0,a.jsx)(s.default,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)(l.default,{}),(0,a.jsx)(c.default,{}),(0,a.jsx)(d.default,{})]})}},1376:(e,t,r)=>{Promise.resolve().then(r.bind(r,6656)),Promise.resolve().then(r.bind(r,4257)),Promise.resolve().then(r.bind(r,8560)),Promise.resolve().then(r.bind(r,1575)),Promise.resolve().then(r.bind(r,2048)),Promise.resolve().then(r.bind(r,8101)),Promise.resolve().then(r.bind(r,5479))},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return n}});let a=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function n(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,r,n;for(let a of e.split("/"))if(r=i.find(e=>a.startsWith(e))){[t,n]=e.split(r,2);break}if(!t||!r||!n)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,a.normalizeAppPath)(t),r){case"(.)":n="/"===t?"/"+n:t+"/"+n;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});n=t.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});n=s.slice(0,-2).concat(n).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:n}}},1575:(e,t,r)=>{"use strict";r.d(t,{default:()=>ea});var a=r(687),i=r(3210),n=r(331),s=r(9523),o=r(2789);class l{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>e.finished))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e){let t=this.animations.map(t=>t.attachTimeline(e));return()=>{t.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class c extends l{then(e,t){return this.finished.finally(e).then(()=>{})}}var d=r(2769),u=r(8267),p=r(1008),m=r(7690),h=r(5723),x=r(5927),f=r(7211),g=r(6244);let y=(e,t,r)=>{let a=t-e;return((r-e)%a+a)%a+e};var v=r(9527);function b(e,t){return(0,v.h)(e)?e[y(0,e.length,t)]:e}var j=r(4068),w=r(9292);function N(e){return"object"==typeof e&&!Array.isArray(e)}function A(e,t,r,a){return"string"==typeof e&&N(t)?(0,w.K)(e,r,a):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}function E(e,t,r,a){return"number"==typeof t?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):"<"===t?r:t.startsWith("<")?Math.max(0,r+parseFloat(t.slice(1))):a.get(t)??e}var P=r(8028),R=r(7556);function k(e,t){return e.at!==t.at?e.at-t.at:null===e.value?1:null===t.value?-1:0}function _(e,t){return t.has(e)||t.set(e,{}),t.get(e)}function O(e,t){return t[e]||(t[e]=[]),t[e]}let T=e=>"number"==typeof e,M=e=>e.every(T);var C=r(2699),S=r(1565),I=r(4156),D=r(3905),$=r(515),z=r(4538),V=r(9542);class L extends V.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(e,t){if(t in e){let r=e[t];if("string"==typeof r||"number"==typeof r)return r}}getBaseTargetFromProps(){}removeValueFromRenderState(e,t){delete t.output[e]}measureInstanceViewportBox(){return(0,z.ge)()}build(e,t){Object.assign(e.output,t)}renderInstance(e,{output:t}){Object.assign(e,t)}sortInstanceNodePosition(){return 0}}var U=r(8778);function W(e){let t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},r=(0,I.x)(e)&&!(0,D.h)(e)?new U.l(t):new $.M(t);r.mount(e),C.C.set(e,r)}function F(e){let t=new L({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});t.mount(e),C.C.set(e,t)}var K=r(5944);function q(e,t,r,a){let i=[];if((0,x.S)(e)||"number"==typeof e||"string"==typeof e&&!N(t))i.push((0,K.z)(e,N(t)&&t.default||t,r&&r.default||r));else{let n=A(e,t,a),s=n.length;(0,g.V)(!!s,"No valid elements provided.");for(let e=0;e<s;e++){let a=n[e],o=a instanceof Element?W:F;C.C.has(a)||o(a);let l=C.C.get(a),c={...r};"delay"in c&&"function"==typeof c.delay&&(c.delay=c.delay(e,s)),i.push(...(0,S.$)(l,{...t,transition:c},{}))}}return i}function H(e){return function(t,r,a){let i=[],n=new c(i=Array.isArray(t)&&t.some(Array.isArray)?function(e,t,r){let a=[];return(function(e,{defaultTransition:t={},...r}={},a,i){let n=t.duration||.3,s=new Map,o=new Map,l={},c=new Map,d=0,y=0,v=0;for(let r=0;r<e.length;r++){let s=e[r];if("string"==typeof s){c.set(s,y);continue}if(!Array.isArray(s)){c.set(s.name,E(y,s.at,d,c));continue}let[j,k,T={}]=s;void 0!==T.at&&(y=E(y,T.at,d,c));let C=0,S=(e,r,a,s=0,o=0)=>{var l;let c=Array.isArray(l=e)?l:[l],{delay:d=0,times:x=(0,u.Z)(c),type:j="keyframes",repeat:w,repeatType:N,repeatDelay:A=0,...E}=r,{ease:k=t.ease||"easeOut",duration:_}=r,O="function"==typeof d?d(s,o):d,T=c.length,S=(0,p.W)(j)?j:i?.[j||"keyframes"];if(T<=2&&S){let e=100;2===T&&M(c)&&(e=Math.abs(c[1]-c[0]));let t={...E};void 0!==_&&(t.duration=(0,f.f)(_));let r=(0,m.X)(t,e,S);k=r.ease,_=r.duration}_??(_=n);let I=y+O;1===x.length&&0===x[0]&&(x[1]=1);let D=x.length-c.length;if(D>0&&(0,h.f)(x,D),1===c.length&&c.unshift(null),w){(0,g.V)(w<20,"Repeat count too high, must be less than 20");_*=w+1;let e=[...c],t=[...x],r=[...k=Array.isArray(k)?[...k]:[k]];for(let a=0;a<w;a++){c.push(...e);for(let i=0;i<e.length;i++)x.push(t[i]+(a+1)),k.push(0===i?"linear":b(r,i-1))}for(let e=0;e<x.length;e++)x[e]=x[e]/(w+1)}let $=I+_;!function(e,t,r,a,i,n){for(let t=0;t<e.length;t++){let r=e[t];r.at>i&&r.at<n&&((0,R.Ai)(e,r),t--)}for(let s=0;s<t.length;s++)e.push({value:t[s],at:(0,P.k)(i,n,a[s]),easing:b(r,s)})}(a,c,k,x,I,$),C=Math.max(O+_,C),v=Math.max($,v)};if((0,x.S)(j))S(k,T,O("default",_(j,o)));else{let e=A(j,k,a,l),t=e.length;for(let r=0;r<t;r++){let a=_(e[r],o);for(let e in k){var w,N;S(k[e],(w=T,N=e,w&&w[N]?{...w,...w[N]}:{...w}),O(e,a),r,t)}}}d=y,y+=C}return o.forEach((e,a)=>{for(let i in e){let n=e[i];n.sort(k);let o=[],l=[],c=[];for(let e=0;e<n.length;e++){let{at:t,value:r,easing:a}=n[e];o.push(r),l.push((0,j.q)(0,v,t)),c.push(a||"easeOut")}0!==l[0]&&(l.unshift(0),o.unshift(o[0]),c.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),o.push(null)),s.has(a)||s.set(a,{keyframes:{},transition:{}});let d=s.get(a);d.keyframes[i]=o,d.transition[i]={...t,duration:v,ease:c,times:l,...r}}}),s})(e,t,r,{spring:d.o}).forEach(({keyframes:e,transition:t},r)=>{a.push(...q(r,e,t))}),a}(t,r,e):q(t,r,a,e));return e&&e.animations.push(n),n}}H();var G=r(4819),X=r(4780);let Q=({words:e,className:t,filter:r=!0,duration:s=.5})=>{let[l,c]=function(){var e;let t=(0,o.M)(()=>({current:null,animations:[]})),r=(0,o.M)(()=>H(t));return e=()=>{t.animations.forEach(e=>e.stop())},(0,i.useEffect)(()=>()=>e(),[]),[t,r]}(),d=e.split(" ");return(0,i.useEffect)(()=>{c("span",{opacity:1,filter:r?"blur(0px)":"none"},{duration:s||1,delay:function(e=.1,{startDelay:t=0,from:r=0,ease:a}={}){return(i,n)=>{let s=e*Math.abs(("number"==typeof r?r:function(e,t){if("first"===e)return 0;{let r=t-1;return"last"===e?r:r/2}}(r,n))-i);if(a){let t=n*e;s=(0,G.K)(a)(s/t)*t}return t+s}}(.2)})},[l.current,c,s,r]),(0,a.jsx)("div",{className:(0,X.cn)("font-bold",t),children:(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("div",{className:"dark:text-white text-black text-2xl leading-snug tracking-wide",children:(0,a.jsx)(n.P.div,{ref:l,children:d.map((e,t)=>(0,a.jsxs)(n.P.span,{className:"dark:text-white text-black opacity-0",style:{filter:r?"blur(10px)":"none"},children:[e," "]},e+t))})})})})};var B=r(7822),Z=r(4158),Y=r(3502),J=r(21),ee=r(7390),et=r(427),er=r(9147);let ea=()=>{let e=[{icon:(0,a.jsx)(Z.A,{className:"w-6 h-6"}),title:"Mobile Service",description:"We come to your location"},{icon:(0,a.jsx)(Y.A,{className:"w-6 h-6"}),title:"DOT Certified",description:"Fully compliant testing"},{icon:(0,a.jsx)(J.A,{className:"w-6 h-6"}),title:"Fast Results",description:"Quick turnaround times"},{icon:(0,a.jsx)(ee.A,{className:"w-6 h-6"}),title:"Accurate Testing",description:"Medical-grade precision"}];return(0,a.jsxs)("section",{id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"}),(0,a.jsx)(n.P.div,{className:"absolute top-20 left-10 w-72 h-72 bg-primary-500/10 rounded-full blur-3xl",animate:{x:[0,100,0],y:[0,-50,0]},transition:{duration:20,repeat:1/0,ease:"easeInOut"}}),(0,a.jsx)(n.P.div,{className:"absolute bottom-20 right-10 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl",animate:{x:[0,-80,0],y:[0,60,0]},transition:{duration:25,repeat:1/0,ease:"easeInOut"}}),(0,a.jsx)("div",{className:"container mx-auto px-6 relative z-10",children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(n.P.div,{className:"space-y-8",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)(n.P.div,{className:"inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.6},children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 mr-2"}),"Trusted by 500+ Businesses"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h1",{className:"text-5xl lg:text-7xl font-bold leading-tight",children:[(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Kalanis"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-neutral-900",children:"Express"})]}),(0,a.jsx)("div",{className:"text-xl lg:text-2xl text-neutral-600 max-w-2xl",children:(0,a.jsx)(Q,{words:"Professional Mobile Drug & DNA Testing Services - We Come to You",className:"text-xl lg:text-2xl text-neutral-600"})})]}),(0,a.jsx)(n.P.p,{className:"text-lg text-neutral-500 max-w-xl leading-relaxed",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.6},children:"DOT Compliant • Certified Technicians • Accurate Results • Convenient Scheduling"}),(0,a.jsxs)(n.P.div,{className:"flex flex-col sm:flex-row gap-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1,duration:.6},children:[(0,a.jsx)(s.$,{size:"lg",variant:"primary",icon:(0,a.jsx)(et.A,{className:"w-5 h-5"}),className:"text-lg px-8 py-4",children:"Schedule Testing"}),(0,a.jsx)(s.$,{size:"lg",variant:"outline",icon:(0,a.jsx)(er.A,{className:"w-5 h-5"}),className:"text-lg px-8 py-4",children:"Call Now: (*************"})]}),(0,a.jsxs)(n.P.div,{className:"flex items-center space-x-6 pt-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.6},children:[(0,a.jsxs)("div",{className:"text-sm text-neutral-500",children:[(0,a.jsx)("div",{className:"font-semibold text-neutral-900",children:"24/7"}),(0,a.jsx)("div",{children:"Available"})]}),(0,a.jsx)("div",{className:"w-px h-8 bg-neutral-300"}),(0,a.jsxs)("div",{className:"text-sm text-neutral-500",children:[(0,a.jsx)("div",{className:"font-semibold text-neutral-900",children:"Same Day"}),(0,a.jsx)("div",{children:"Service"})]}),(0,a.jsx)("div",{className:"w-px h-8 bg-neutral-300"}),(0,a.jsxs)("div",{className:"text-sm text-neutral-500",children:[(0,a.jsx)("div",{className:"font-semibold text-neutral-900",children:"100%"}),(0,a.jsx)("div",{children:"Compliant"})]})]})]}),(0,a.jsx)(n.P.div,{className:"grid grid-cols-2 gap-6",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4,ease:"easeOut"},children:e.map((e,t)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6+.1*t,duration:.6},children:(0,a.jsx)(B.r,{className:"p-6 rounded-3xl",children:(0,a.jsx)("div",{className:"bg-white/80 backdrop-blur-xl rounded-2xl p-6 h-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center space-y-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-2xl bg-primary-100 text-primary-600",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-neutral-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:e.description})]})]})})})},e.title))})]})}),(0,a.jsx)(n.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.5,duration:.6},children:(0,a.jsx)(n.P.div,{className:"w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,a.jsx)(n.P.div,{className:"w-1 h-3 bg-neutral-400 rounded-full mt-2",animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0}})})})]})}},1648:(e,t,r)=>{Promise.resolve().then(r.bind(r,3040)),Promise.resolve().then(r.bind(r,4295)),Promise.resolve().then(r.bind(r,2006)),Promise.resolve().then(r.bind(r,3427)),Promise.resolve().then(r.bind(r,3888)),Promise.resolve().then(r.bind(r,9579)),Promise.resolve().then(r.bind(r,4647))},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return h},normalizeMetadataRoute:function(){return m}});let a=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),n=r(6341),s=r(4396),o=r(660),l=r(4722),c=r(2958),d=r(5499);function u(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function p(e,t,r){let a=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(a,{prefixRouteKeys:!1}),d=(0,n.interpolateDynamicPath)(a,t,o),{name:p,ext:m}=i.default.parse(r),h=u(i.default.posix.join(e,p)),x=h?`-${h}`:"";return(0,c.normalizePathSep)(i.default.join(d,`${p}${x}${m}`))}function m(e){if(!(0,a.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=u(e),!t.endsWith("/route")){let{dir:e,name:a,ext:n}=i.default.parse(t);t=i.default.posix.join(e,`${a}${r?`-${r}`:""}${n}`,"route")}return t}function h(e,t){let r=e.endsWith("/route"),a=r?e.slice(0,-6):e,i=a.endsWith("/sitemap")?".xml":"";return(t?`${a}/[__metadata_id__]`:`${a}${i}`)+(r?"/route":"")}},2006:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\footer.tsx","default")},2048:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var a=r(687);r(3210);var i=r(331),n=r(4493),s=r(9523),o=r(427),l=r(4158),c=r(8710),d=(0,r(6445).A)("outline","file-text","IconFileText",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}],["path",{d:"M9 9l1 0",key:"svg-2"}],["path",{d:"M9 13l6 0",key:"svg-3"}],["path",{d:"M9 17l6 0",key:"svg-4"}]]),u=r(21),p=r(3502),m=r(9147),h=r(3050);let x=()=>{let e=[{step:"01",icon:(0,a.jsx)(o.A,{className:"w-8 h-8"}),title:"Schedule Online",description:"Book your appointment through our easy online system or call our 24/7 support line. Choose a time that works for you.",details:["Online booking system","Flexible scheduling","Same-day availability","24/7 customer support"],color:"primary"},{step:"02",icon:(0,a.jsx)(l.A,{className:"w-8 h-8"}),title:"We Come to You",description:"Our certified technician arrives at your chosen location with all necessary equipment and documentation.",details:["Mobile service anywhere","Certified technicians","Professional equipment","Proper documentation"],color:"secondary"},{step:"03",icon:(0,a.jsx)(c.A,{className:"w-8 h-8"}),title:"Professional Collection",description:"Quick, professional sample collection following strict protocols to ensure accuracy and compliance.",details:["Chain of custody","Sterile procedures","Quick collection","Privacy maintained"],color:"accent"},{step:"04",icon:(0,a.jsx)(d,{className:"w-8 h-8"}),title:"Fast Results",description:"Receive accurate results quickly through our secure portal or direct communication as preferred.",details:["Secure result delivery","Fast turnaround","Multiple delivery options","Legal documentation"],color:"primary"}],t=[{icon:(0,a.jsx)(u.A,{className:"w-6 h-6"}),title:"Save Time",description:"No travel required - we come to your location"},{icon:(0,a.jsx)(p.A,{className:"w-6 h-6"}),title:"Guaranteed Accuracy",description:"Medical-grade testing with certified technicians"},{icon:(0,a.jsx)(m.A,{className:"w-6 h-6"}),title:"24/7 Support",description:"Round-the-clock customer service and scheduling"}];return(0,a.jsx)("section",{id:"process",className:"py-24 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-6",children:[(0,a.jsxs)(i.P.div,{className:"text-center max-w-4xl mx-auto mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)(i.P.div,{className:"inline-flex items-center px-4 py-2 rounded-full bg-secondary-100 text-secondary-700 text-sm font-medium mb-6",initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},viewport:{once:!0},transition:{delay:.2,duration:.6},children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Simple Process"]}),(0,a.jsxs)("h2",{className:"text-4xl lg:text-6xl font-bold mb-6",children:[(0,a.jsx)("span",{className:"text-neutral-900",children:"How It"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Works"})]}),(0,a.jsx)("p",{className:"text-xl text-neutral-600 leading-relaxed",children:"Our streamlined process makes testing convenient and stress-free. From booking to results, we handle everything professionally."})]}),(0,a.jsxs)("div",{className:"relative mb-20",children:[(0,a.jsx)("div",{className:"hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-200 via-secondary-200 to-accent-200 transform -translate-y-1/2 z-0"}),(0,a.jsx)("div",{className:"grid lg:grid-cols-4 gap-8 relative z-10",children:e.map((e,t)=>(0,a.jsx)(i.P.div,{className:"relative",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,delay:.2*t,ease:"easeOut"},children:(0,a.jsx)(n.Zp,{variant:"luxury",className:"text-center h-full group",children:(0,a.jsxs)(n.Wu,{className:"p-8",children:[(0,a.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${"primary"===e.color?"bg-primary-500":"secondary"===e.color?"bg-secondary-500":"bg-accent-500"}`,children:e.step})}),(0,a.jsx)("div",{className:`p-4 rounded-2xl w-fit mx-auto mb-6 mt-4 ${"primary"===e.color?"bg-primary-100 text-primary-600":"secondary"===e.color?"bg-secondary-100 text-secondary-600":"bg-accent-100 text-accent-600"}`,children:e.icon}),(0,a.jsx)("h3",{className:"text-xl font-bold text-neutral-900 mb-4",children:e.title}),(0,a.jsx)("p",{className:"text-neutral-600 mb-6 leading-relaxed",children:e.description}),(0,a.jsx)("ul",{className:"space-y-2 text-sm text-neutral-500",children:e.details.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center justify-center",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 text-secondary-500 mr-2 flex-shrink-0"}),e]},t))})]})})},e.step))})]}),(0,a.jsx)(i.P.div,{className:"grid md:grid-cols-3 gap-8 mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:t.map((e,t)=>(0,a.jsxs)(i.P.div,{className:"text-center",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t,ease:"easeOut"},children:[(0,a.jsx)("div",{className:"p-4 rounded-2xl bg-primary-100 text-primary-600 w-fit mx-auto mb-4",children:e.icon}),(0,a.jsx)("h4",{className:"text-lg font-semibold text-neutral-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-neutral-600",children:e.description})]},e.title))}),(0,a.jsxs)(i.P.div,{className:"text-center bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-8 lg:p-12",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsx)("h3",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 mb-4",children:"Ready to Get Started?"}),(0,a.jsx)("p",{className:"text-xl text-neutral-600 mb-8 max-w-2xl mx-auto",children:"Schedule your testing appointment today and experience the convenience of professional mobile testing services."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(s.$,{size:"lg",variant:"primary",icon:(0,a.jsx)(o.A,{className:"w-5 h-5"}),children:"Schedule Testing"}),(0,a.jsx)(s.$,{size:"lg",variant:"outline",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"}),iconPosition:"right",children:"View Pricing"})]})]})]})})}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let a=r(5362);function i(e,t){let r=[],i=(0,a.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),n=(0,a.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,a)=>{if("string"!=typeof e)return!1;let i=n(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...a,...i.params}}}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,a]of e.entries()){let e=t[r];void 0===e?t[r]=a:Array.isArray(e)?e.push(a):t[r]=[e,a]}return t}function a(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,a(e));else t.set(r,a(i));return t}function n(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,a]of t.entries())e.append(r,a)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return n},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3040:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\about.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\about.tsx","default")},3050:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","arrow-right","IconArrowRight",[["path",{d:"M5 12l14 0",key:"svg-0"}],["path",{d:"M13 18l6 -6",key:"svg-1"}],["path",{d:"M13 6l6 6",key:"svg-2"}]])},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,a=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(a,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3427:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\hero.tsx","default")},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let a=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),n=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:s,searchParams:o,search:l,hash:c,href:d,origin:u}=new URL(e,n);if(u!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,a.searchParamsToUrlQuery)(o):void 0,search:l,hash:c,href:d.slice(u.length)}}},3873:e=>{"use strict";e.exports=require("path")},3888:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\process.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\process.tsx","default")},4158:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","map-pin","IconMapPin",[["path",{d:"M9 11a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-0"}],["path",{d:"M17.657 16.657l-4.243 4.243a2 2 0 0 1 -2.827 0l-4.244 -4.243a8 8 0 1 1 11.314 0z",key:"svg-1"}]])},4257:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var a=r(687);r(3210);var i=r(331),n=r(4493),s=r(9523),o=r(7822),l=r(9147),c=r(1080),d=r(4158),u=r(21),p=(0,r(6445).A)("outline","send","IconSend",[["path",{d:"M10 14l11 -11",key:"svg-0"}],["path",{d:"M21 3l-6.5 18a.55 .55 0 0 1 -1 0l-3.5 -7l-7 -3.5a.55 .55 0 0 1 0 -1l18 -6.5",key:"svg-1"}]]),m=r(427),h=r(3502),x=r(7390);let f=()=>{let e=[{icon:(0,a.jsx)(l.A,{className:"w-6 h-6"}),title:"Call Us",info:"(*************",description:"24/7 Support Available",action:"tel:+15551234567"},{icon:(0,a.jsx)(c.A,{className:"w-6 h-6"}),title:"Email Us",info:"<EMAIL>",description:"Quick Response Guaranteed",action:"mailto:<EMAIL>"},{icon:(0,a.jsx)(d.A,{className:"w-6 h-6"}),title:"Service Area",info:"Metro Area & Surrounding",description:"Mobile Service Available",action:null},{icon:(0,a.jsx)(u.A,{className:"w-6 h-6"}),title:"Hours",info:"24/7 Emergency Service",description:"Flexible Scheduling",action:null}];return(0,a.jsx)("section",{id:"contact",className:"py-24 bg-gradient-to-br from-primary-50 via-white to-secondary-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-6",children:[(0,a.jsxs)(i.P.div,{className:"text-center max-w-4xl mx-auto mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)(i.P.div,{className:"inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-6",initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},viewport:{once:!0},transition:{delay:.2,duration:.6},children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Get In Touch"]}),(0,a.jsxs)("h2",{className:"text-4xl lg:text-6xl font-bold mb-6",children:[(0,a.jsx)("span",{className:"text-neutral-900",children:"Ready to"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Get Started?"})]}),(0,a.jsx)("p",{className:"text-xl text-neutral-600 leading-relaxed",children:"Contact us today to schedule your testing appointment or learn more about our professional mobile testing services."})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 mb-16",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:(0,a.jsx)(o.r,{children:(0,a.jsx)(n.Zp,{variant:"luxury",className:"p-8",children:(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 mb-6",children:"Schedule Your Testing"}),(0,a.jsxs)("form",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"First Name"}),(0,a.jsx)("input",{type:"text",className:"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200",placeholder:"John"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"Last Name"}),(0,a.jsx)("input",{type:"text",className:"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200",placeholder:"Doe"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",className:"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",className:"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200",placeholder:"(*************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"Service Needed"}),(0,a.jsxs)("select",{className:"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200",children:[(0,a.jsx)("option",{children:"Select a service"}),(0,a.jsx)("option",{children:"DOT Drug Testing"}),(0,a.jsx)("option",{children:"DNA Testing"}),(0,a.jsx)("option",{children:"Mobile Collection"}),(0,a.jsx)("option",{children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"Message"}),(0,a.jsx)("textarea",{rows:4,className:"w-full px-4 py-3 rounded-2xl border border-neutral-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20 transition-all duration-200",placeholder:"Tell us about your testing needs..."})]}),(0,a.jsx)(s.$,{size:"lg",variant:"primary",className:"w-full",icon:(0,a.jsx)(p,{className:"w-5 h-5"}),children:"Send Message"})]})]})})})}),(0,a.jsxs)(i.P.div,{className:"space-y-8",initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.8,delay:.2,ease:"easeOut"},children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 mb-6",children:"Contact Information"}),(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.4+.1*t,ease:"easeOut"},children:(0,a.jsx)(n.Zp,{hover:!0,className:"p-6",children:(0,a.jsxs)(n.Wu,{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-2xl bg-primary-100 text-primary-600 flex-shrink-0",children:e.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-900",children:e.title}),(0,a.jsx)("p",{className:"text-lg text-primary-600 font-medium",children:e.info}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:e.description})]}),e.action&&(0,a.jsx)(s.$,{variant:"ghost",size:"sm",onClick:()=>window.open(e.action,"_self"),children:"Contact"})]})})},e.title))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-neutral-900 mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(s.$,{size:"lg",variant:"primary",icon:(0,a.jsx)(m.A,{className:"w-5 h-5"}),className:"flex-1",children:"Schedule Now"}),(0,a.jsx)(s.$,{size:"lg",variant:"secondary",icon:(0,a.jsx)(l.A,{className:"w-5 h-5"}),className:"flex-1",children:"Call Now"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-xl font-bold text-neutral-900 mb-4",children:"Our Services"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:["DOT Drug Testing","DNA Testing","Mobile Collection","Same-Day Service","Court-Ordered Testing","Workplace Testing"].map(e=>(0,a.jsxs)("div",{className:"flex items-center text-neutral-600",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 text-secondary-500 mr-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:e})]},e))})]})]})]}),(0,a.jsxs)(i.P.div,{className:"text-center bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl p-8 lg:p-12 text-white",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsx)(x.A,{className:"w-16 h-16 mx-auto mb-6 opacity-80"}),(0,a.jsx)("h3",{className:"text-3xl lg:text-4xl font-bold mb-4",children:"Need Emergency Testing?"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"We provide 24/7 emergency testing services for urgent situations. Call now for immediate assistance."}),(0,a.jsx)(s.$,{size:"lg",variant:"accent",icon:(0,a.jsx)(l.A,{className:"w-5 h-5"}),className:"bg-white text-primary-600 hover:bg-neutral-100",children:"Emergency Line: (*************"})]})]})})}},4295:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\contact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\contact.tsx","default")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return x},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return u},parseParameter:function(){return l}});let a=r(6143),i=r(1437),n=r(3293),s=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function d(e,t,r){let a={},l=1,d=[];for(let u of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>u.startsWith(e)),s=u.match(o);if(e&&s&&s[2]){let{key:t,optional:r,repeat:i}=c(s[2]);a[t]={pos:l++,repeat:i,optional:r},d.push("/"+(0,n.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:i}=c(s[2]);a[e]={pos:l++,repeat:t,optional:i},r&&s[1]&&d.push("/"+(0,n.escapeStringRegexp)(s[1]));let o=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&s[1]&&(o=o.substring(1)),d.push(o)}else d.push("/"+(0,n.escapeStringRegexp)(u));t&&s&&s[3]&&d.push((0,n.escapeStringRegexp)(s[3]))}return{parameterizedRoute:d.join(""),groups:a}}function u(e,t){let{includeSuffix:r=!1,includePrefix:a=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:n,groups:s}=d(e,r,a),o=n;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function p(e){let t,{interceptionMarker:r,getSafeRouteKey:a,segment:i,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:d,optional:u,repeat:p}=c(i),m=d.replace(/\W/g,"");o&&(m=""+o+m);let h=!1;(0===m.length||m.length>30)&&(h=!0),isNaN(parseInt(m.slice(0,1)))||(h=!0),h&&(m=a());let x=m in s;o?s[m]=""+o+d:s[m]=d;let f=r?(0,n.escapeStringRegexp)(r):"";return t=x&&l?"\\k<"+m+">":p?"(?<"+m+">.+?)":"(?<"+m+">[^/]+?)",u?"(?:/"+f+t+")?":"/"+f+t}function m(e,t,r,l,c){let d,u=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),m={},h=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2])h.push(p({getSafeRouteKey:u,interceptionMarker:s[1],segment:s[2],routeKeys:m,keyPrefix:t?a.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(s&&s[2]){l&&s[1]&&h.push("/"+(0,n.escapeStringRegexp)(s[1]));let e=p({getSafeRouteKey:u,segment:s[2],routeKeys:m,keyPrefix:t?a.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&s[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,n.escapeStringRegexp)(d));r&&s&&s[3]&&h.push((0,n.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:m}}function h(e,t){var r,a,i;let n=m(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(a=t.includePrefix)&&a,null!=(i=t.backreferenceDuplicateKeys)&&i),s=n.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...u(e,t),namedRegex:"^"+s+"$",routeKeys:n.routeKeys}}function x(e,t){let{parameterizedRoute:r}=d(e,!1,!1),{catchAll:a=!0}=t;if("/"===r)return{namedRegex:"^/"+(a?".*":"")+"$"};let{namedParameterizedRoute:i}=m(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(a?"(?:(/.*)?)":"")+"$"}}},4493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>u,Wu:()=>p,ZB:()=>d,Zp:()=>l,aR:()=>c});var a=r(687),i=r(3210),n=r.n(i),s=r(331),o=r(4780);let l=n().forwardRef(({children:e,className:t,variant:r="default",hover:i=!0,padding:n="md",...l},c)=>{let d=(0,o.cn)("relative rounded-3xl transition-all duration-500 ease-out","border border-neutral-200/50"),u={default:(0,o.cn)("bg-white/80 backdrop-blur-xl","shadow-luxury",i&&"hover:shadow-luxury-lg hover:scale-[1.02] hover:-translate-y-2"),glass:(0,o.cn)("bg-white/10 backdrop-blur-md border-white/20","shadow-luxury",i&&"hover:shadow-luxury-lg hover:scale-[1.02]"),luxury:(0,o.cn)("bg-gradient-to-br from-white via-primary-50/30 to-secondary-50/30","backdrop-blur-xl border-primary-200/30","shadow-luxury",i&&"hover:shadow-luxury-lg hover:shadow-glow hover:scale-[1.02] hover:-translate-y-2"),medical:(0,o.cn)("bg-gradient-to-br from-white via-secondary-50/50 to-primary-50/30","backdrop-blur-xl border-secondary-200/30","shadow-luxury",i&&"hover:shadow-luxury-lg hover:shadow-glow-green hover:scale-[1.02] hover:-translate-y-2")},p=(0,o.cn)(d,u[r],{none:"",sm:"p-4",md:"p-6",lg:"p-8",xl:"p-12"}[n],t);return(0,a.jsxs)(s.P.div,{ref:c,className:p,initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:"easeOut"},...l,children:["luxury"===r&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary-50/20 to-secondary-50/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100"}),(0,a.jsx)("div",{className:"relative z-10",children:e})]})});l.displayName="Card";let c=n().forwardRef(({children:e,className:t,...r},i)=>(0,a.jsx)("div",{ref:i,className:(0,o.cn)("flex flex-col space-y-1.5 pb-6",t),...r,children:e}));c.displayName="CardHeader";let d=n().forwardRef(({children:e,className:t,gradient:r=!1,...i},n)=>(0,a.jsx)("h3",{ref:n,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r&&"text-gradient-luxury",t),...i,children:e}));d.displayName="CardTitle";let u=n().forwardRef(({children:e,className:t,...r},i)=>(0,a.jsx)("p",{ref:i,className:(0,o.cn)("text-neutral-600 leading-relaxed",t),...r,children:e}));u.displayName="CardDescription";let p=n().forwardRef(({children:e,className:t,...r},i)=>(0,a.jsx)("div",{ref:i,className:(0,o.cn)("",t),...r,children:e}));p.displayName="CardContent",n().forwardRef(({children:e,className:t,...r},i)=>(0,a.jsx)("div",{ref:i,className:(0,o.cn)("flex items-center pt-6",t),...r,children:e})).displayName="CardFooter"},4647:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\testimonials.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\testimonials.tsx","default")},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return n},normalizeRscURL:function(){return s}});let a=r(5531),i=r(5499);function n(e){return(0,a.ensureLeadingSlash)(e.split("/").reduce((e,t,r,a)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===a.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return x},PageNotFoundError:function(){return f},SP:function(){return p},ST:function(){return m},WEB_VITALS:function(){return r},execOnce:function(){return a},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return n},isResSent:function(){return c},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function a(e){let t,r=!1;return function(){for(var a=arguments.length,i=Array(a),n=0;n<a;n++)i[n]=arguments[n];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,n=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let a=await e.getInitialProps(t);if(r&&c(r))return a;if(!a)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+a+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a}let p="undefined"!=typeof performance,m=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class x extends Error{}class f extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var a=e[r];if("*"===a||"+"===a||"?"===a){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===a){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===a){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===a){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===a){for(var i="",n=r+1;n<e.length;){var s=e.charCodeAt(n);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[n++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=n;continue}if("("===a){var o=1,l="",n=r+1;if("?"===e[n])throw TypeError('Pattern cannot start with "?" at '+n);for(;n<e.length;){if("\\"===e[n]){l+=e[n++]+e[n++];continue}if(")"===e[n]){if(0==--o){n++;break}}else if("("===e[n]&&(o++,"?"!==e[n+1]))throw TypeError("Capturing groups are not allowed at "+n);l+=e[n++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=n;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),a=t.prefixes,n=void 0===a?"./":a,s="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,c=0,d="",u=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},p=function(e){var t=u(e);if(void 0!==t)return t;var a=r[c];throw TypeError("Unexpected "+a.type+" at "+a.index+", expected "+e)},m=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var h=u("CHAR"),x=u("NAME"),f=u("PATTERN");if(x||f){var g=h||"";-1===n.indexOf(g)&&(d+=g,g=""),d&&(o.push(d),d=""),o.push({name:x||l++,prefix:g,suffix:"",pattern:f||s,modifier:u("MODIFIER")||""});continue}var y=h||u("ESCAPED_CHAR");if(y){d+=y;continue}if(d&&(o.push(d),d=""),u("OPEN")){var g=m(),v=u("NAME")||"",b=u("PATTERN")||"",j=m();p("CLOSE"),o.push({name:v||(b?l++:""),pattern:v&&!b?s:b,prefix:g,suffix:j,modifier:u("MODIFIER")||""});continue}p("END")}return o}function r(e,t){void 0===t&&(t={});var r=n(t),a=t.encode,i=void 0===a?function(e){return e}:a,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",a=0;a<e.length;a++){var n=e[a];if("string"==typeof n){r+=n;continue}var s=t?t[n.name]:void 0,c="?"===n.modifier||"*"===n.modifier,d="*"===n.modifier||"+"===n.modifier;if(Array.isArray(s)){if(!d)throw TypeError('Expected "'+n.name+'" to not repeat, but got an array');if(0===s.length){if(c)continue;throw TypeError('Expected "'+n.name+'" to not be empty')}for(var u=0;u<s.length;u++){var p=i(s[u],n);if(o&&!l[a].test(p))throw TypeError('Expected all "'+n.name+'" to match "'+n.pattern+'", but got "'+p+'"');r+=n.prefix+p+n.suffix}continue}if("string"==typeof s||"number"==typeof s){var p=i(String(s),n);if(o&&!l[a].test(p))throw TypeError('Expected "'+n.name+'" to match "'+n.pattern+'", but got "'+p+'"');r+=n.prefix+p+n.suffix;continue}if(!c){var m=d?"an array":"a string";throw TypeError('Expected "'+n.name+'" to be '+m)}}return r}}function a(e,t,r){void 0===r&&(r={});var a=r.decode,i=void 0===a?function(e){return e}:a;return function(r){var a=e.exec(r);if(!a)return!1;for(var n=a[0],s=a.index,o=Object.create(null),l=1;l<a.length;l++)!function(e){if(void 0!==a[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=a[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):o[r.name]=i(a[e],r)}}(l);return{path:n,index:s,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function n(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var a=r.strict,s=void 0!==a&&a,o=r.start,l=r.end,c=r.encode,d=void 0===c?function(e){return e}:c,u="["+i(r.endsWith||"")+"]|$",p="["+i(r.delimiter||"/#?")+"]",m=void 0===o||o?"^":"",h=0;h<e.length;h++){var x=e[h];if("string"==typeof x)m+=i(d(x));else{var f=i(d(x.prefix)),g=i(d(x.suffix));if(x.pattern)if(t&&t.push(x),f||g)if("+"===x.modifier||"*"===x.modifier){var y="*"===x.modifier?"?":"";m+="(?:"+f+"((?:"+x.pattern+")(?:"+g+f+"(?:"+x.pattern+"))*)"+g+")"+y}else m+="(?:"+f+"("+x.pattern+")"+g+")"+x.modifier;else m+="("+x.pattern+")"+x.modifier;else m+="(?:"+f+g+")"+x.modifier}}if(void 0===l||l)s||(m+=p+"?"),m+=r.endsWith?"(?="+u+")":"$";else{var v=e[e.length-1],b="string"==typeof v?p.indexOf(v[v.length-1])>-1:void 0===v;s||(m+="(?:"+p+"(?="+u+"))?"),b||(m+="(?="+p+"|"+u+")")}return new RegExp(m,n(r))}function o(t,r,a){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,a).source}).join("|")+")",n(a)):s(e(t,a),r,a)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,a){return r(e(t,a),a)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return a(o(e,r,t),r,t)},t.regexpToFunction=a,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5479:(e,t,r)=>{"use strict";r.d(t,{default:()=>O});var a=r(687),i=r(3210),n=r(331),s=r(4493),o=r(4342),l=r(2582),c=r(2789);function d(e){let t=(0,c.M)(()=>(0,o.OQ)(e)),{isStatic:r}=(0,i.useContext)(l.Q);if(r){let[,r]=(0,i.useState)(e);(0,i.useEffect)(()=>t.on("change",r),[])}return t}var u=r(5927),p=r(3303),m=r(3671);function h(e){return"number"==typeof e?e:parseFloat(e)}var x=r(9331),f=r(5124);function g(e,t){let r=d(t()),a=()=>r.set(t());return a(),(0,f.E)(()=>{let t=()=>m.Gt.preRender(a,!1,!0),r=e.map(e=>e.on("change",t));return()=>{r.forEach(e=>e()),(0,m.WG)(a)}}),r}function y(e,t,r,a){if("function"==typeof e){o.bt.current=[],e();let t=g(o.bt.current,e);return o.bt.current=void 0,t}let i="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),r=t?0:-1,a=e[0+r],i=e[1+r],n=e[2+r],s=e[3+r],o=(0,x.G)(i,n,s);return t?o(a):o}(t,r,a);return Array.isArray(e)?v(e,i):v([e],([e])=>i(e))}function v(e,t){let r=(0,c.M)(()=>[]);return g(e,()=>{r.length=0;let a=e.length;for(let t=0;t<a;t++)r[t]=e[t].get();return t(r)})}function b(e,t={}){let{isStatic:r}=(0,i.useContext)(l.Q),a=()=>(0,u.S)(e)?e.get():e;if(r)return y(a);let n=d(a());return(0,i.useInsertionEffect)(()=>(function(e,t,r){let a,i,n=e.get(),s=null,o=n,l="string"==typeof n?n.replace(/[\d.-]/g,""):void 0,c=()=>{s&&(s.stop(),s=null)},d=()=>{c(),s=new p.s({keyframes:[h(e.get()),h(o)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...r,onUpdate:a})};return e.attach((t,r)=>(o=t,a=e=>{var t,a;return r((t=e,(a=l)?t+a:t))},m.Gt.postRender(d),e.get()),c),(0,u.S)(t)&&(i=t.on("change",t=>{var r,a;return e.set((r=t,(a=l)?r+a:r))}),e.on("destroy",i)),i})(n,e,t),[n,JSON.stringify(t)]),n}var j=r(8920),w=r(4780);let N=({items:e,className:t})=>{let[r,s]=(0,i.useState)(null),o={stiffness:100,damping:5},l=d(0),c=b(y(l,[-100,100],[-45,45]),o),u=b(y(l,[-100,100],[-50,50]),o),p=e=>{let t=e.currentTarget.offsetWidth/2;l.set(e.nativeEvent.offsetX-t)};return(0,a.jsx)("div",{className:(0,w.cn)("flex flex-row items-center justify-center",t),children:e.map((e,t)=>(0,a.jsxs)("div",{className:"group relative -mr-4",onMouseEnter:()=>s(e.id),onMouseLeave:()=>s(null),children:[(0,a.jsx)(j.N,{mode:"popLayout",children:r===e.id&&(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20,scale:.6},animate:{opacity:1,y:0,scale:1,transition:{type:"spring",stiffness:260,damping:10}},exit:{opacity:0,y:20,scale:.6},style:{translateX:u,rotate:c,whiteSpace:"nowrap"},className:"absolute -top-16 left-1/2 z-50 flex -translate-x-1/2 flex-col items-center justify-center rounded-md bg-black px-4 py-2 text-xs shadow-xl",children:[(0,a.jsx)("div",{className:"absolute inset-x-10 -bottom-px z-30 h-px w-[20%] bg-gradient-to-r from-transparent via-emerald-500 to-transparent"}),(0,a.jsx)("div",{className:"absolute -bottom-px left-10 z-30 h-px w-[40%] bg-gradient-to-r from-transparent via-sky-500 to-transparent"}),(0,a.jsx)("div",{className:"relative z-30 text-base font-bold text-white",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-white",children:e.designation})]})}),(0,a.jsx)("img",{onMouseMove:p,height:100,width:100,src:e.image,alt:e.name,className:"relative !m-0 h-14 w-14 rounded-full border-2 border-white object-cover object-top !p-0 transition duration-500 group-hover:z-30 group-hover:scale-105"})]},e.name))})};var A=r(3502),E=r(7390),P=r(6445),R=(0,P.A)("outline","star","IconStar",[["path",{d:"M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z",key:"svg-0"}]]),k=r(21),_=(0,P.A)("outline","quote","IconQuote",[["path",{d:"M10 11h-4a1 1 0 0 1 -1 -1v-3a1 1 0 0 1 1 -1h3a1 1 0 0 1 1 1v6c0 2.667 -1.333 4.333 -4 5",key:"svg-0"}],["path",{d:"M19 11h-4a1 1 0 0 1 -1 -1v-3a1 1 0 0 1 1 -1h3a1 1 0 0 1 1 1v6c0 2.667 -1.333 4.333 -4 5",key:"svg-1"}]]);let O=()=>{let e=[{number:"500+",label:"Businesses Served",icon:(0,a.jsx)(A.A,{className:"w-6 h-6"})},{number:"10,000+",label:"Tests Completed",icon:(0,a.jsx)(E.A,{className:"w-6 h-6"})},{number:"99.9%",label:"Accuracy Rate",icon:(0,a.jsx)(R,{className:"w-6 h-6"})},{number:"24/7",label:"Support Available",icon:(0,a.jsx)(k.A,{className:"w-6 h-6"})}];return(0,a.jsx)("section",{className:"py-24 bg-gradient-to-br from-neutral-50 via-white to-primary-50/30",children:(0,a.jsxs)("div",{className:"container mx-auto px-6",children:[(0,a.jsxs)(n.P.div,{className:"text-center max-w-4xl mx-auto mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)(n.P.div,{className:"inline-flex items-center px-4 py-2 rounded-full bg-accent-100 text-accent-700 text-sm font-medium mb-6",initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},viewport:{once:!0},transition:{delay:.2,duration:.6},children:[(0,a.jsx)(R,{className:"w-4 h-4 mr-2"}),"Trusted by Professionals"]}),(0,a.jsxs)("h2",{className:"text-4xl lg:text-6xl font-bold mb-6",children:[(0,a.jsx)("span",{className:"text-neutral-900",children:"What Our"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Clients Say"})]}),(0,a.jsx)("p",{className:"text-xl text-neutral-600 leading-relaxed",children:"Don't just take our word for it. See what businesses and individuals say about our professional testing services."})]}),(0,a.jsx)(n.P.div,{className:"grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:e.map((e,t)=>(0,a.jsxs)(n.P.div,{className:"text-center",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t,ease:"easeOut"},children:[(0,a.jsx)("div",{className:"p-4 rounded-2xl bg-primary-100 text-primary-600 w-fit mx-auto mb-4",children:e.icon}),(0,a.jsx)("div",{className:"text-3xl lg:text-4xl font-bold text-neutral-900 mb-2",children:e.number}),(0,a.jsx)("div",{className:"text-neutral-600 font-medium",children:e.label})]},e.label))}),(0,a.jsx)(n.P.div,{className:"grid lg:grid-cols-2 gap-8 mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[{name:"Sarah Johnson",role:"HR Director",company:"TransLogistics Inc.",content:"Kalanis Express has been a game-changer for our DOT compliance. Their mobile service saves us hours of employee downtime, and their technicians are always professional and thorough.",rating:5,avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"},{name:"Michael Chen",role:"Safety Manager",company:"BuildRight Construction",content:"The convenience of on-site testing is incredible. We can maintain our safety standards without disrupting our work schedule. Highly recommend their services.",rating:5,avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},{name:"Jennifer Martinez",role:"Legal Assistant",company:"Martinez & Associates",content:"For court-ordered DNA testing, Kalanis Express provides the professionalism and accuracy we need. Their documentation is always perfect for legal proceedings.",rating:5,avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"},{name:"David Thompson",role:"Fleet Manager",company:"Metro Delivery Services",content:"Same-day service and accurate results every time. Their mobile testing has streamlined our hiring process and keeps our drivers compliant with DOT regulations.",rating:5,avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"}].map((e,t)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t,ease:"easeOut"},children:(0,a.jsx)(s.Zp,{variant:"luxury",className:"h-full",children:(0,a.jsxs)(s.Wu,{className:"p-8",children:[(0,a.jsx)(_,{className:"w-8 h-8 text-primary-500 mb-4"}),(0,a.jsx)("div",{className:"flex items-center mb-4",children:[...Array(e.rating)].map((e,t)=>(0,a.jsx)(R,{className:"w-5 h-5 text-accent-500 fill-current"},t))}),(0,a.jsxs)("p",{className:"text-neutral-700 leading-relaxed mb-6 text-lg",children:['"                    “',e.content,'”"']}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("img",{src:e.avatar,alt:e.name,className:"w-12 h-12 rounded-full object-cover mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold text-neutral-900",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-neutral-600",children:[e.role,", ",e.company]})]})]})]})})},e.name))}),(0,a.jsxs)(n.P.div,{className:"text-center mb-12",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsx)("h3",{className:"text-3xl font-bold text-neutral-900 mb-4",children:"Meet Our Certified Team"}),(0,a.jsx)("p",{className:"text-lg text-neutral-600 max-w-2xl mx-auto mb-8",children:"Our experienced professionals ensure accurate results and exceptional service"}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(N,{items:[{id:1,name:"Dr. Sarah Wilson",designation:"Medical Director",image:"https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150&h=150&fit=crop&crop=face"},{id:2,name:"Mike Rodriguez",designation:"Lead Technician",image:"https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face"},{id:3,name:"Lisa Chen",designation:"Quality Assurance",image:"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face"},{id:4,name:"James Parker",designation:"Mobile Technician",image:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"}]})})]}),(0,a.jsx)(n.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[{name:"DOT Certified",description:"Department of Transportation approved testing procedures"},{name:"SAMHSA Guidelines",description:"Following Substance Abuse and Mental Health Services guidelines"},{name:"Chain of Custody",description:"Strict protocols for sample handling and documentation"},{name:"Legal Compliance",description:"Court-admissible results and proper documentation"}].map((e,t)=>(0,a.jsxs)(n.P.div,{className:"text-center p-6 rounded-2xl bg-white border border-neutral-200 hover:border-primary-300 transition-colors duration-300",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t,ease:"easeOut"},children:[(0,a.jsx)(A.A,{className:"w-8 h-8 text-secondary-500 mx-auto mb-3"}),(0,a.jsx)("h4",{className:"font-semibold text-neutral-900 mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-neutral-600",children:e.description})]},e.name))})]})})}},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return d},matchHas:function(){return c},parseDestination:function(){return u},prepareDestination:function(){return p}});let a=r(5362),i=r(3293),n=r(6759),s=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,a){void 0===r&&(r=[]),void 0===a&&(a=[]);let i={},n=r=>{let a,n=r.key;switch(r.type){case"header":n=n.toLowerCase(),a=e.headers[n];break;case"cookie":a="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":a=t[n];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};a=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&a)return i[function(e){let t="";for(let r=0;r<e.length;r++){let a=e.charCodeAt(r);(a>64&&a<91||a>96&&a<123)&&(t+=e[r])}return t}(n)]=a,!0;if(a){let e=RegExp("^"+r.value+"$"),t=Array.isArray(a)?a.slice(-1)[0].match(e):a.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>n(e))||a.some(e=>n(e)))&&i}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,a.compile)("/"+e,{validate:!1})(t).slice(1)}function u(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,n.parseUrl)(t),a=r.pathname;a&&(a=l(a));let s=r.href;s&&(s=l(s));let o=r.hostname;o&&(o=l(o));let c=r.hash;return c&&(c=l(c)),{...r,pathname:a,hostname:o,href:s,hash:c}}function p(e){let t,r,i=Object.assign({},e.query),n=u(e),{hostname:o,query:c}=n,p=n.pathname;n.hash&&(p=""+p+n.hash);let m=[],h=[];for(let e of((0,a.pathToRegexp)(p,h),h))m.push(e.name);if(o){let e=[];for(let t of((0,a.pathToRegexp)(o,e),e))m.push(t.name)}let x=(0,a.compile)(p,{validate:!1});for(let[r,i]of(o&&(t=(0,a.compile)(o,{validate:!1})),Object.entries(c)))Array.isArray(i)?c[r]=i.map(t=>d(l(t),e.params)):"string"==typeof i&&(c[r]=d(l(i),e.params));let f=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!f.some(e=>m.includes(e)))for(let t of f)t in c||(c[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let r=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[a,i]=(r=x(e.params)).split("#",2);t&&(n.hostname=t(e.params)),n.pathname=a,n.hash=(i?"#":"")+(i||""),delete n.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return n.query={...i,...n.query},{newUrl:r,destQuery:c,parsedDestination:n}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return f},interpolateDynamicPath:function(){return h},normalizeDynamicRouteParams:function(){return x},normalizeVercelUrl:function(){return m}});let a=r(9551),i=r(1959),n=r(2437),s=r(4396),o=r(8034),l=r(5526),c=r(2887),d=r(4722),u=r(6143),p=r(7912);function m(e,t,r){let i=(0,a.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let a=e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX),n=e!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(a||n||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,a.format)(i)}function h(e,t,r){if(!r)return e;for(let a of Object.keys(r.groups)){let i,{optional:n,repeat:s}=r.groups[a],o=`[${s?"...":""}${a}]`;n&&(o=`[${o}]`);let l=t[a];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,i)}return e}function x(e,t,r,a){let i={};for(let n of Object.keys(t.groups)){let s=e[n];"string"==typeof s?s=(0,d.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(d.normalizeRscURL));let o=r[n],l=t.groups[n].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&a))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${n}]]`))&&(s=void 0,delete e[n]),s&&"string"==typeof s&&t.groups[n].repeat&&(s=s.split("/")),s&&(i[n]=s)}return{params:i,hasValidParams:!0}}function f({page:e,i18n:t,basePath:r,rewrites:a,pageIsDynamic:d,trailingSlash:u,caseSensitive:f}){let g,y,v;return d&&(g=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(y=(0,o.getRouteMatcher)(g))(e)),{handleRewrites:function(s,o){let p={},m=o.pathname,h=a=>{let c=(0,n.getPathMatch)(a.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!f});if(!o.pathname)return!1;let h=c(o.pathname);if((a.has||a.missing)&&h){let e=(0,l.matchHas)(s,o.query,a.has,a.missing);e?Object.assign(h,e):h=!1}if(h){let{parsedDestination:n,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:a.destination,params:h,query:o.query});if(n.protocol)return!0;if(Object.assign(p,s,h),Object.assign(o.query,n.query),delete n.query,Object.assign(o,n),!(m=o.pathname))return!1;if(r&&(m=m.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(m,t.locales);m=e.pathname,o.query.nextInternalLocale=e.detectedLocale||h.nextInternalLocale}if(m===e)return!0;if(d&&y){let e=y(m);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of a.beforeFiles||[])h(e);if(m!==e){let t=!1;for(let e of a.afterFiles||[])if(t=h(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(m||"");return t===(0,c.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of a.fallback||[])if(t=h(e))break}}return p},defaultRouteRegex:g,dynamicRouteMatcher:y,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:r}=g,a=(0,o.getRouteMatcher)({re:{exec:e=>{let a=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(a)){let r=(0,p.normalizeNextQueryParam)(e);r&&(a[r]=t,delete a[e])}let i={};for(let e of Object.keys(r)){let n=r[e];if(!n)continue;let s=t[n],o=a[e];if(!s.optional&&!o)return null;i[s.pos]=o}return i}},groups:t})(e);return a||null},normalizeDynamicRouteParams:(e,t)=>g&&v?x(e,g,v,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>m(e,t,g),interpolateDynamicPath:(e,t)=>h(e,t,g)}}function g(e,t){return"string"==typeof e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},n=t.split(a),s=(r||{}).decode||e,o=0;o<n.length;o++){var l=n[o],c=l.indexOf("=");if(!(c<0)){var d=l.substr(0,c).trim(),u=l.substr(++c,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==i[d]&&(i[d]=function(e,t){try{return t(e)}catch(t){return e}}(u,s))}}return i},t.serialize=function(e,t,a){var n=a||{},s=n.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(n.domain){if(!i.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!i.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,a=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6656:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var a=r(687);r(3210);var i=r(331),n=r(4493),s=r(9523),o=r(7822),l=r(3502),c=r(6445),d=(0,c.A)("outline","heart","IconHeart",[["path",{d:"M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572",key:"svg-0"}]]),u=r(21),p=r(7390),m=(0,c.A)("outline","award","IconAward",[["path",{d:"M12 9m-6 0a6 6 0 1 0 12 0a6 6 0 1 0 -12 0",key:"svg-0"}],["path",{d:"M12 15l3.4 5.89l1.598 -3.233l3.598 .232l-3.4 -5.889",key:"svg-1"}],["path",{d:"M6.802 12l-3.4 5.89l3.598 -.233l1.598 3.232l3.4 -5.889",key:"svg-2"}]]),h=r(7831),x=(0,c.A)("outline","target","IconTarget",[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 12m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0",key:"svg-1"}],["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-2"}]]),f=r(9147);let g=()=>{let e=[{icon:(0,a.jsx)(l.A,{className:"w-8 h-8"}),title:"Accuracy",description:"Medical-grade precision in every test we perform, ensuring reliable results you can trust."},{icon:(0,a.jsx)(d,{className:"w-8 h-8"}),title:"Compassion",description:"Understanding that testing can be stressful, we provide caring, professional service."},{icon:(0,a.jsx)(u.A,{className:"w-8 h-8"}),title:"Convenience",description:"Mobile service that comes to you, saving time and reducing stress for our clients."},{icon:(0,a.jsx)(p.A,{className:"w-8 h-8"}),title:"Compliance",description:"Strict adherence to DOT regulations and legal requirements for defensible results."}],t=[{icon:(0,a.jsx)(m,{className:"w-6 h-6"}),title:"Industry Recognition",description:"Certified by leading health organizations"},{icon:(0,a.jsx)(h.A,{className:"w-6 h-6"}),title:"Experienced Team",description:"Over 50 years combined experience"},{icon:(0,a.jsx)(x,{className:"w-6 h-6"}),title:"Proven Results",description:"99.9% accuracy rate across all testing"}];return(0,a.jsx)("section",{id:"about",className:"py-24 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-6",children:[(0,a.jsxs)(i.P.div,{className:"text-center max-w-4xl mx-auto mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)(i.P.div,{className:"inline-flex items-center px-4 py-2 rounded-full bg-secondary-100 text-secondary-700 text-sm font-medium mb-6",initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},viewport:{once:!0},transition:{delay:.2,duration:.6},children:[(0,a.jsx)(d,{className:"w-4 h-4 mr-2"}),"About Kalanis Express"]}),(0,a.jsxs)("h2",{className:"text-4xl lg:text-6xl font-bold mb-6",children:[(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Professional"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-neutral-900",children:"Testing Excellence"})]}),(0,a.jsx)("p",{className:"text-xl text-neutral-600 leading-relaxed",children:"Founded on the principles of accuracy, convenience, and compassion, Kalanis Express brings professional testing services directly to you."})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,a.jsxs)(i.P.div,{className:"space-y-6",initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsx)("h3",{className:"text-3xl font-bold text-neutral-900",children:"Our Story"}),(0,a.jsxs)("div",{className:"space-y-4 text-lg text-neutral-600 leading-relaxed",children:[(0,a.jsx)("p",{children:"Kalanis Express was born from a simple observation: traditional testing services were inconvenient, time-consuming, and often stressful for both businesses and individuals."}),(0,a.jsx)("p",{children:"Our founder, Sylonda, recognized the need for a more convenient approach. With years of experience in healthcare and a passion for helping others, she created a mobile testing service that brings professional, accurate testing directly to your location."}),(0,a.jsx)("p",{children:"Today, we serve hundreds of businesses and individuals across the region, providing DOT-compliant drug testing, accurate DNA testing, and exceptional customer service that puts your needs first."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 pt-4",children:[(0,a.jsx)(s.$,{size:"lg",variant:"primary",icon:(0,a.jsx)(f.A,{className:"w-5 h-5"}),children:"Contact Us"}),(0,a.jsx)(s.$,{size:"lg",variant:"outline",children:"Learn More"})]})]}),(0,a.jsx)(i.P.div,{className:"space-y-6",initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.8,delay:.2,ease:"easeOut"},children:(0,a.jsx)(o.r,{children:(0,a.jsx)(n.Zp,{variant:"luxury",className:"p-8",children:(0,a.jsxs)(n.Wu,{className:"space-y-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-neutral-900 text-center",children:"Why Choose Us"}),t.map((e,t)=>(0,a.jsxs)(i.P.div,{className:"flex items-start space-x-4",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.4+.1*t,ease:"easeOut"},children:[(0,a.jsx)("div",{className:"p-3 rounded-2xl bg-primary-100 text-primary-600 flex-shrink-0",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-900 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-neutral-600",children:e.description})]})]},e.title))]})})})})]}),(0,a.jsxs)(i.P.div,{className:"text-center mb-12",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsx)("h3",{className:"text-3xl font-bold text-neutral-900 mb-4",children:"Our Core Values"}),(0,a.jsx)("p",{className:"text-lg text-neutral-600 max-w-2xl mx-auto",children:"These principles guide everything we do and ensure exceptional service for every client"})]}),(0,a.jsx)(i.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:e.map((e,t)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,delay:.1*t,ease:"easeOut"},children:(0,a.jsx)(n.Zp,{hover:!0,className:"text-center h-full",children:(0,a.jsxs)(n.Wu,{className:"p-8",children:[(0,a.jsx)("div",{className:"p-4 rounded-2xl bg-secondary-100 text-secondary-600 w-fit mx-auto mb-6",children:e.icon}),(0,a.jsx)("h4",{className:"text-xl font-semibold text-neutral-900 mb-4",children:e.title}),(0,a.jsx)("p",{className:"text-neutral-600 leading-relaxed",children:e.description})]})})},e.title))})]})})}},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return n}});let a=r(2785),i=r(3736);function n(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,a.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7390:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","certificate","IconCertificate",[["path",{d:"M15 15m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-0"}],["path",{d:"M13 17.5v4.5l2 -1.5l2 1.5v-4.5",key:"svg-1"}],["path",{d:"M10 19h-5a2 2 0 0 1 -2 -2v-10c0 -1.1 .9 -2 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -1 1.73",key:"svg-2"}],["path",{d:"M6 9l12 0",key:"svg-3"}],["path",{d:"M6 12l3 0",key:"svg-4"}],["path",{d:"M6 15l2 0",key:"svg-5"}]])},7822:(e,t,r)=>{"use strict";r.d(t,{r:()=>s});var a=r(687),i=r(4780);r(3210);var n=r(331);let s=({children:e,className:t,containerClassName:r,animate:s=!0})=>{let o={initial:{backgroundPosition:"0 50%"},animate:{backgroundPosition:["0, 50%","100% 50%","0 50%"]}};return(0,a.jsxs)("div",{className:(0,i.cn)("relative p-[4px] group",r),children:[(0,a.jsx)(n.P.div,{variants:s?o:void 0,initial:s?"initial":void 0,animate:s?"animate":void 0,transition:s?{duration:5,repeat:1/0,repeatType:"reverse"}:void 0,style:{backgroundSize:s?"400% 400%":void 0},className:(0,i.cn)("absolute inset-0 rounded-3xl z-[1] opacity-60 group-hover:opacity-100 blur-xl transition duration-500 will-change-transform","bg-[radial-gradient(circle_farthest-side_at_0_100%,#00ccb1,transparent),radial-gradient(circle_farthest-side_at_100%_0,#7b61ff,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#ffc414,transparent),radial-gradient(circle_farthest-side_at_0_0,#1ca0fb,#141316)]")}),(0,a.jsx)(n.P.div,{variants:s?o:void 0,initial:s?"initial":void 0,animate:s?"animate":void 0,transition:s?{duration:5,repeat:1/0,repeatType:"reverse"}:void 0,style:{backgroundSize:s?"400% 400%":void 0},className:(0,i.cn)("absolute inset-0 rounded-3xl z-[1] will-change-transform","bg-[radial-gradient(circle_farthest-side_at_0_100%,#00ccb1,transparent),radial-gradient(circle_farthest-side_at_100%_0,#7b61ff,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#ffc414,transparent),radial-gradient(circle_farthest-side_at_0_0,#1ca0fb,#141316)]")}),(0,a.jsx)("div",{className:(0,i.cn)("relative z-10",t),children:e})]})}},7831:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]])},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let a=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let n=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new a.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>n(e)):s[e]=n(r))}return s}}},8101:(e,t,r)=>{"use strict";r.d(t,{default:()=>y});var a=r(687);r(3210);var i=r(331),n=r(4493),s=r(9523),o=r(8710),l=r(6445),c=(0,l.A)("outline","dna","IconDna",[["path",{d:"M14.828 14.828a4 4 0 1 0 -5.656 -5.656a4 4 0 0 0 5.656 5.656z",key:"svg-0"}],["path",{d:"M9.172 20.485a4 4 0 1 0 -5.657 -5.657",key:"svg-1"}],["path",{d:"M14.828 3.515a4 4 0 0 0 5.657 5.657",key:"svg-2"}]]),d=(0,l.A)("outline","truck","IconTruck",[["path",{d:"M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-1"}],["path",{d:"M5 17h-2v-11a1 1 0 0 1 1 -1h9v12m-4 0h6m4 0h2v-6h-8m0 -5h5l3 5",key:"svg-2"}]]),u=(0,l.A)("outline","building","IconBuilding",[["path",{d:"M3 21l18 0",key:"svg-0"}],["path",{d:"M9 8l1 0",key:"svg-1"}],["path",{d:"M9 12l1 0",key:"svg-2"}],["path",{d:"M9 16l1 0",key:"svg-3"}],["path",{d:"M14 8l1 0",key:"svg-4"}],["path",{d:"M14 12l1 0",key:"svg-5"}],["path",{d:"M14 16l1 0",key:"svg-6"}],["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16",key:"svg-7"}]]),p=r(7831),m=r(3502),h=r(7390),x=r(3050),f=r(427),g=r(21);let y=()=>{let e=[{icon:(0,a.jsx)(o.A,{className:"w-8 h-8"}),title:"DOT Drug Testing",description:"Department of Transportation compliant drug and alcohol testing for commercial drivers and safety-sensitive employees.",features:["5-Panel & 10-Panel Testing","Breath Alcohol Testing","Random Testing Programs","Return-to-Duty Testing"],color:"primary"},{icon:(0,a.jsx)(c,{className:"w-8 h-8"}),title:"DNA Testing",description:"Accurate paternity and relationship testing with legally defensible results for personal or court-ordered cases.",features:["Paternity Testing","Sibling DNA Testing","Grandparent Testing","Legal & Personal Testing"],color:"secondary"},{icon:(0,a.jsx)(d,{className:"w-8 h-8"}),title:"Mobile Collection",description:"Convenient on-site collection services at your workplace, home, or any location that works for you.",features:["Workplace Testing","Home Collection","Same-Day Service","Flexible Scheduling"],color:"accent"}],t=[{icon:(0,a.jsx)(u,{className:"w-6 h-6"}),title:"Transportation",description:"DOT compliance for trucking, aviation, and maritime companies"},{icon:(0,a.jsx)(p.A,{className:"w-6 h-6"}),title:"Healthcare",description:"Pre-employment and random testing for medical facilities"},{icon:(0,a.jsx)(m.A,{className:"w-6 h-6"}),title:"Construction",description:"Safety-focused testing for construction and industrial sites"},{icon:(0,a.jsx)(h.A,{className:"w-6 h-6"}),title:"Legal Services",description:"Court-ordered testing and legal documentation support"}],r={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},l={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}};return(0,a.jsx)("section",{id:"services",className:"py-24 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-6",children:[(0,a.jsxs)(i.P.div,{className:"text-center max-w-4xl mx-auto mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)(i.P.div,{className:"inline-flex items-center px-6 py-3 rounded-full bg-primary-100 text-primary-700 text-sm font-semibold mb-8 shadow-sm",initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},viewport:{once:!0},transition:{delay:.2,duration:.6},children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Professional Testing Services"]}),(0,a.jsxs)("h2",{className:"text-4xl lg:text-6xl font-bold mb-8",children:[(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Comprehensive"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-neutral-900",children:"Testing Solutions"})]}),(0,a.jsx)("p",{className:"text-xl text-neutral-700 leading-relaxed max-w-3xl mx-auto",children:"From DOT compliance to DNA testing, we provide accurate, reliable results with the convenience of mobile service. Our certified technicians ensure professional standards every time."})]}),(0,a.jsx)(i.P.div,{className:"grid lg:grid-cols-3 gap-8 mb-20",variants:r,initial:"hidden",whileInView:"visible",viewport:{once:!0},children:e.map(e=>(0,a.jsx)(i.P.div,{variants:l,children:(0,a.jsxs)(n.Zp,{className:"h-full bg-white border-2 border-neutral-100 hover:border-primary-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group",children:[(0,a.jsxs)(n.aR,{className:"pb-4",children:[(0,a.jsx)("div",{className:"p-4 rounded-2xl w-fit mb-4 bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg",children:e.icon}),(0,a.jsx)(n.ZB,{className:"text-2xl font-bold text-neutral-900 mb-3",children:e.title}),(0,a.jsx)(n.BT,{className:"text-base text-neutral-700 leading-relaxed",children:e.description})]}),(0,a.jsxs)(n.Wu,{className:"pt-0",children:[(0,a.jsx)("ul",{className:"space-y-3 mb-8",children:e.features.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center text-neutral-800",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full bg-secondary-100 flex items-center justify-center mr-3 flex-shrink-0",children:(0,a.jsx)(m.A,{className:"w-3 h-3 text-secondary-600"})}),(0,a.jsx)("span",{className:"font-medium",children:e})]},t))}),(0,a.jsx)(s.$,{variant:"primary",size:"lg",className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300",icon:(0,a.jsx)(x.A,{className:"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200"}),iconPosition:"right",children:"Learn More"})]})]})},e.title))}),(0,a.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsx)("h3",{className:"text-4xl font-bold text-neutral-900 mb-6",children:"Industries We Serve"}),(0,a.jsx)("p",{className:"text-xl text-neutral-700 max-w-3xl mx-auto leading-relaxed",children:"Trusted by businesses across multiple industries for reliable testing services"})]}),(0,a.jsx)(i.P.div,{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",variants:r,initial:"hidden",whileInView:"visible",viewport:{once:!0},children:t.map(e=>(0,a.jsx)(i.P.div,{variants:l,children:(0,a.jsx)(n.Zp,{className:"text-center h-full bg-white border-2 border-neutral-100 hover:border-primary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group",children:(0,a.jsxs)(n.Wu,{className:"p-8",children:[(0,a.jsx)("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 text-white w-fit mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,a.jsx)("h4",{className:"font-bold text-neutral-900 mb-3 text-lg",children:e.title}),(0,a.jsx)("p",{className:"text-neutral-700 leading-relaxed",children:e.description})]})})},e.title))}),(0,a.jsx)(i.P.div,{className:"text-center",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-primary-500 to-secondary-500 rounded-3xl p-8 lg:p-12 text-white",children:[(0,a.jsx)("h3",{className:"text-3xl lg:text-4xl font-bold mb-4",children:"Ready to Schedule Your Testing?"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Get started with professional, convenient testing services. Same-day appointments available."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(s.$,{size:"lg",variant:"accent",icon:(0,a.jsx)(f.A,{className:"w-5 h-5"}),className:"bg-white text-primary-600 hover:bg-neutral-100",children:"Schedule Now"}),(0,a.jsx)(s.$,{size:"lg",variant:"outline",icon:(0,a.jsx)(g.A,{className:"w-5 h-5"}),className:"border-white text-white hover:bg-white hover:text-primary-600",children:"24/7 Support"})]})]})})]})})}},8212:(e,t,r)=>{"use strict";function a(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:a}=r(6415);return a(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return a}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return u},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return d}});let a=r(2958),i=r(4722),n=r(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let i=(r?"":"?")+"$",n=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${s.icon.filename}${n}${l(s.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${s.apple.filename}${n}${l(s.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${s.openGraph.filename}${n}${l(s.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${s.twitter.filename}${n}${l(s.twitter.extensions,t)}${i}`)],c=(0,a.normalizePathSep)(e);return o.some(e=>e.test(c))}function d(e){let t=e.replace(/\/route$/,"");return(0,n.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function u(e){return!(0,n.isAppRouteRoute)(e)&&c(e,[],!1)}function p(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,n.isAppRouteRoute)(e)&&c(t,[],!1)}},8560:(e,t,r)=>{"use strict";r.d(t,{default:()=>y});var a=r(687);r(3210);var i=r(331),n=r(9523),s=r(6445),o=(0,s.A)("outline","brand-facebook","IconBrandFacebook",[["path",{d:"M7 10v4h3v7h4v-7h3l1 -4h-4v-2a1 1 0 0 1 1 -1h3v-4h-3a5 5 0 0 0 -5 5v2h-3",key:"svg-0"}]]),l=(0,s.A)("outline","brand-twitter","IconBrandTwitter",[["path",{d:"M22 4.01c-1 .49 -1.98 .689 -3 .99c-1.121 -1.265 -2.783 -1.335 -4.38 -.737s-2.643 2.06 -2.62 3.737v1c-3.245 .083 -6.135 -1.395 -8 -4c0 0 -4.182 7.433 4 11c-1.872 1.247 -3.739 2.088 -6 2c3.308 1.803 6.913 2.423 10.034 1.517c3.58 -1.04 6.522 -3.723 7.651 -7.742a13.84 13.84 0 0 0 .497 -3.753c0 -.249 1.51 -2.772 1.818 -4.013z",key:"svg-0"}]]),c=(0,s.A)("outline","brand-linkedin","IconBrandLinkedin",[["path",{d:"M8 11v5",key:"svg-0"}],["path",{d:"M8 8v.01",key:"svg-1"}],["path",{d:"M12 16v-5",key:"svg-2"}],["path",{d:"M16 16v-3a2 2 0 1 0 -4 0",key:"svg-3"}],["path",{d:"M3 7a4 4 0 0 1 4 -4h10a4 4 0 0 1 4 4v10a4 4 0 0 1 -4 4h-10a4 4 0 0 1 -4 -4z",key:"svg-4"}]]),d=(0,s.A)("outline","brand-instagram","IconBrandInstagram",[["path",{d:"M4 8a4 4 0 0 1 4 -4h8a4 4 0 0 1 4 4v8a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}],["path",{d:"M16.5 7.5v.01",key:"svg-2"}]]),u=r(9147),p=r(1080),m=r(4158),h=r(21),x=r(3502),f=r(7390),g=(0,s.A)("outline","arrow-up","IconArrowUp",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M18 11l-6 -6",key:"svg-1"}],["path",{d:"M6 11l6 -6",key:"svg-2"}]]);let y=()=>{let e=[{icon:(0,a.jsx)(o,{className:"w-5 h-5"}),href:"#",name:"Facebook"},{icon:(0,a.jsx)(l,{className:"w-5 h-5"}),href:"#",name:"Twitter"},{icon:(0,a.jsx)(c,{className:"w-5 h-5"}),href:"#",name:"LinkedIn"},{icon:(0,a.jsx)(d,{className:"w-5 h-5"}),href:"#",name:"Instagram"}];return(0,a.jsxs)("footer",{className:"bg-neutral-900 text-white",children:[(0,a.jsx)("div",{className:"container mx-auto px-6 py-16",children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-12",children:[(0,a.jsxs)(i.P.div,{className:"lg:col-span-1",initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,ease:"easeOut"},children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h3",{className:"text-3xl font-bold",children:[(0,a.jsx)("span",{className:"text-gradient-luxury",children:"Kalanis"}),(0,a.jsx)("br",{}),(0,a.jsx)("span",{className:"text-white",children:"Express"})]}),(0,a.jsx)("p",{className:"text-neutral-400 mt-4 leading-relaxed",children:"Professional mobile drug and DNA testing services. We bring certified, accurate testing directly to your location."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center text-neutral-300",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 mr-3 text-primary-400"}),(0,a.jsx)("span",{children:"(*************"})]}),(0,a.jsxs)("div",{className:"flex items-center text-neutral-300",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 mr-3 text-primary-400"}),(0,a.jsx)("span",{children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"flex items-center text-neutral-300",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 mr-3 text-primary-400"}),(0,a.jsx)("span",{children:"Metro Area & Surrounding"})]}),(0,a.jsxs)("div",{className:"flex items-center text-neutral-300",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 mr-3 text-primary-400"}),(0,a.jsx)("span",{children:"24/7 Emergency Service"})]})]}),(0,a.jsx)("div",{className:"flex space-x-4 mt-6",children:e.map(e=>(0,a.jsx)(i.P.a,{href:e.href,className:"p-2 rounded-lg bg-neutral-800 text-neutral-400 hover:bg-primary-500 hover:text-white transition-all duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:e.icon},e.name))})]}),(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,delay:.1,ease:"easeOut"},children:[(0,a.jsx)("h4",{className:"text-xl font-semibold mb-6",children:"Quick Links"}),(0,a.jsx)("ul",{className:"space-y-3",children:[{name:"Home",href:"#home"},{name:"Services",href:"#services"},{name:"Process",href:"#process"},{name:"About",href:"#about"},{name:"Contact",href:"#contact"}].map(e=>(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:e.href,className:"text-neutral-400 hover:text-primary-400 transition-colors duration-300",children:e.name})},e.name))})]}),(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,delay:.2,ease:"easeOut"},children:[(0,a.jsx)("h4",{className:"text-xl font-semibold mb-6",children:"Our Services"}),(0,a.jsx)("ul",{className:"space-y-3",children:[{name:"DOT Drug Testing",href:"#dot-testing"},{name:"DNA Testing",href:"#dna-testing"},{name:"Mobile Collection",href:"#mobile-service"},{name:"Emergency Testing",href:"#emergency"},{name:"Workplace Testing",href:"#workplace"}].map(e=>(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:e.href,className:"text-neutral-400 hover:text-secondary-400 transition-colors duration-300",children:e.name})},e.name))})]}),(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.8,delay:.3,ease:"easeOut"},children:[(0,a.jsx)("h4",{className:"text-xl font-semibold mb-6",children:"Stay Updated"}),(0,a.jsx)("p",{className:"text-neutral-400 mb-4",children:"Get updates on new services and industry news."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 mb-6",children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-2 rounded-lg bg-neutral-800 border border-neutral-700 text-white placeholder-neutral-500 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200"}),(0,a.jsx)(n.$,{variant:"primary",size:"sm",className:"whitespace-nowrap",children:"Subscribe"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center text-neutral-300",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 mr-3 text-secondary-400"}),(0,a.jsx)("span",{className:"text-sm",children:"DOT Certified"})]}),(0,a.jsxs)("div",{className:"flex items-center text-neutral-300",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 mr-3 text-secondary-400"}),(0,a.jsx)("span",{className:"text-sm",children:"SAMHSA Guidelines"})]})]})]})]})}),(0,a.jsx)("div",{className:"border-t border-neutral-800",children:(0,a.jsx)("div",{className:"container mx-auto px-6 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("div",{className:"text-neutral-400 text-sm mb-4 md:mb-0",children:"\xa9 2024 Kalanis Express. All rights reserved."}),(0,a.jsx)("div",{className:"flex flex-wrap items-center gap-6",children:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"HIPAA Compliance",href:"/hipaa"},{name:"DOT Regulations",href:"/dot-compliance"}].map(e=>(0,a.jsx)("a",{href:e.href,className:"text-neutral-400 hover:text-primary-400 text-sm transition-colors duration-300",children:e.name},e.name))})]})})}),(0,a.jsx)(i.P.button,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-8 p-3 bg-primary-500 text-white rounded-full shadow-luxury hover:bg-primary-600 transition-all duration-300 z-50",initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)(g,{className:"w-6 h-6"})})]})}},8710:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(6445).A)("outline","flask","IconFlask",[["path",{d:"M9 3l6 0",key:"svg-0"}],["path",{d:"M10 9l4 0",key:"svg-1"}],["path",{d:"M10 3v6l-4 11a.7 .7 0 0 0 .5 1h11a.7 .7 0 0 0 .5 -1l-4 -11v-6",key:"svg-2"}]])},8773:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(5239),i=r(8088),n=r(8170),s=r.n(n),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,7393)),"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9579:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Client websites\\\\Kalanis Express\\\\kalanis-express\\\\src\\\\components\\\\sections\\\\services.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Kalanis Express\\kalanis-express\\src\\components\\sections\\services.tsx","default")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,688,945],()=>r(8773));module.exports=a})();